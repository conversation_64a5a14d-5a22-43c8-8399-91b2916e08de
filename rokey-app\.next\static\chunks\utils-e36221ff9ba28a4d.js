"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5738],{5777:(e,t,r)=>{r.d(t,{zf:()=>l});class n{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{ttl:n=3e5,tags:o=[],priority:a="medium",serialize:i=!1}=r,c={data:i?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:n,accessCount:0,lastAccessed:Date.now(),tags:o,priority:a};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,c)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let r=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:r}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[r,n]of this.cache.entries())n.tags.some(t=>e.includes(t))&&(this.cache.delete(r),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,r)=>e+(t-r.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=this.get(e);if(n)return this.backgroundRefresh(e,t,r),n;let o=await t();return this.set(e,o,r),o}async backgroundRefresh(e,t,r){try{let n=await t();this.set(e,n,r)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort((e,t)=>{let[,r]=e,[,n]=t,o={low:0,medium:1,high:2},a=o[r.priority]-o[n.priority];return 0!==a?a:r.lastAccessed-n.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,r]of this.cache.entries())this.isExpired(r)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}}let o=new n(200),a={static:{ttl:864e5,priority:"high",tags:["static"]},user:{ttl:12e4,priority:"high",tags:["user"]},system:{ttl:3e4,priority:"low",tags:["system"]},pricing:{ttl:36e5,priority:"medium",tags:["pricing"]}},i={LANDING_FEATURES:"landing:features",PRICING_TIERS:"pricing:tiers",PRICING_COMPARISON:"pricing:comparison",SYSTEM_STATUS:"system:status",SYSTEM_MODELS:"system:models",USER_CONFIGS:"user:configs",USER_ANALYTICS:"user:analytics"};class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}trackNavigation(e,t){let r="".concat(e,"->").concat(t),n=this.userBehavior.get(r)||0;this.userBehavior.set(r,n+1),n>2&&this.schedulePrefetch(t)}schedulePrefetch(e){this.prefetchQueue.has(e)||(this.prefetchQueue.add(e),this.processPrefetchQueue())}async processPrefetchQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.size){for(let e of(this.isProcessing=!0,this.prefetchQueue)){try{await this.prefetchRoute(e),this.prefetchQueue.delete(e)}catch(e){}await new Promise(e=>setTimeout(e,100))}this.isProcessing=!1}}async prefetchRoute(e){let t={"/dashboard":()=>this.prefetchDashboardData(),"/pricing":()=>this.prefetchPricingData(),"/auth/signup":()=>this.prefetchAuthData(),"/features":()=>this.prefetchFeaturesData()}[e];t&&await t()}async prefetchDashboardData(){let e=[this.cacheIfNotExists(i.USER_CONFIGS,"/api/custom-configs",a.user),this.cacheIfNotExists(i.USER_ANALYTICS,"/api/analytics",a.user),this.cacheIfNotExists(i.SYSTEM_STATUS,"/api/system-status",a.system)];await Promise.allSettled(e)}async prefetchPricingData(){let e=[this.cacheIfNotExists(i.PRICING_TIERS,"/api/pricing/tiers",a.pricing),this.cacheIfNotExists(i.PRICING_COMPARISON,"/api/pricing/comparison",a.pricing)];await Promise.allSettled(e)}async prefetchAuthData(){let e=[this.cacheIfNotExists(i.PRICING_TIERS,"/api/pricing/tiers",a.pricing)];await Promise.allSettled(e)}async prefetchFeaturesData(){let e=[this.cacheIfNotExists(i.LANDING_FEATURES,"/api/features",a.static),this.cacheIfNotExists(i.SYSTEM_MODELS,"/api/models",a.static)];await Promise.allSettled(e)}async cacheIfNotExists(e,t,r){let n=o.get(e);if(n)return n;try{let n=await fetch(t);if(n.ok){let t=await n.json();return o.set(e,t,r),t}}catch(e){}}constructor(){this.prefetchQueue=new Set,this.isProcessing=!1,this.userBehavior=new Map}}let l=c.getInstance()},13744:(e,t,r)=>{r.d(t,{Lz:()=>a});var n=r(49509);let o=!!("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function a(e){"serviceWorker"in navigator&&new URL(n.env.PUBLIC_URL||"",window.location.href).origin===window.location.origin&&window.addEventListener("load",()=>{var t,r;let n="/sw.js";o?(t=n,r=e,fetch(t,{headers:{"Service-Worker":"script"}}).then(e=>{let n=e.headers.get("content-type");404===e.status||null!=n&&-1===n.indexOf("javascript")?navigator.serviceWorker.ready.then(e=>{e.unregister().then(()=>{window.location.reload()})}):i(t,r)}).catch(()=>{}),navigator.serviceWorker.ready.then(()=>{})):i(n,e)})}function i(e,t){navigator.serviceWorker.register(e).then(e=>{e.onupdatefound=()=>{let r=e.installing;null!=r&&(r.onstatechange=()=>{"installed"===r.state&&(navigator.serviceWorker.controller?t&&t.onUpdate&&t.onUpdate(e):t&&t.onSuccess&&t.onSuccess(e))})}}).catch(e=>{t&&t.onError&&t.onError(e)})}},24403:(e,t,r)=>{r.d(t,{l2:()=>l,mx:()=>c}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let n=new Map,o={hits:0,misses:0},a=new Set,i=!1,c=e=>{let{configId:t,enablePrefetch:r=!0,cacheTimeout:c=3e5,staleTimeout:l=3e4}=e,[s,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[d,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[f,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[O,g]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),p=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),_=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),v=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];arguments.length>2&&void 0!==arguments[2]&&arguments[2];let i=n.get(e),s=Date.now();if(!t&&i&&s-i.timestamp<c)return o.hits++,s-i.timestamp>l&&!i.isStale&&(i.isStale=!0,r&&(a.add(e),E())),i.data;o.misses++,p.current&&p.current.abort(),p.current=new AbortController;try{let t="/api/chat/conversations?custom_api_config_id=".concat(e),r=await fetch(t,{signal:p.current.signal,headers:{"Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"}});if(!r.ok)throw Error("Failed to fetch chat history: ".concat(r.status," ").concat(r.statusText));let o=await r.json();return n.set(e,{data:o,timestamp:s,isStale:!1}),o}catch(e){if("AbortError"===e.name)throw e;if(i&&i.data.length>0)return i.data;throw e}},[c,l,r]),E=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async()=>{if(!i&&0!==a.size){i=!0;try{let e=Array.from(a);for(let t of(a.clear(),e))try{await v(t,!0,!0),await new Promise(e=>setTimeout(e,100))}catch(e){}}finally{i=!1}}},[v]),D=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!t)return;let r=n.get(t);!e&&r&&r.data.length>0&&(u(r.data),m(r.isStale),g(null)),h(!0),_.current=t;try{let r=await v(t,e);_.current===t&&(u(r),m(!1),g(null))}catch(e){"AbortError"!==e.name&&_.current===t&&g("Failed to load chat history: ".concat(e.message))}finally{_.current===t&&h(!1)}},[t,v]),w=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async e=>{r&&(a.add(e),E())},[r,E]),N=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{e?n.delete(e):n.clear()},[]),U=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>({size:n.size,hits:o.hits,misses:o.misses}),[]);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t?D():(u([]),h(!1),g(null),m(!1))},[t,D]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>()=>{p.current&&p.current.abort()},[]),{chatHistory:s,isLoading:d,isStale:f,error:O,refetch:D,prefetch:w,invalidateCache:N,getCacheStats:U}},l=()=>{let e=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(new Set);return{prefetchChatHistory:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async t=>{e.current.has(t)||(e.current.add(t),a.add(t),setTimeout(()=>{a.size>0&&(async()=>{if(!i){i=!0;try{let e=Array.from(a);for(let t of(a.clear(),e)){try{let e="/api/chat/conversations?custom_api_config_id=".concat(t),r=await fetch(e,{headers:{"X-Prefetch":"true"}});if(r.ok){let e=await r.json();n.set(t,{data:e,timestamp:Date.now(),isStale:!1})}}catch(e){}await new Promise(e=>setTimeout(e,100))}}finally{i=!1}}})()},200))},[])}}},28003:(e,t,r)=>{r.d(t,{_:()=>o}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let n={};function o(){let[e,t]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({}),r=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({}),o=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),a=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),i=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async function(e){var i;let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(o(e))return a(e);if(null==(i=n[e])?void 0:i.isLoading)return null;r.current[e]&&r.current[e].abort();let l=new AbortController;r.current[e]=l,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===c?await new Promise(e=>setTimeout(e,200)):"medium"===c&&await new Promise(e=>setTimeout(e,50));let[r,o,a,i,s]=await Promise.allSettled([fetch("/api/custom-configs",{signal:l.signal}),fetch("/api/keys?custom_config_id=".concat(e),{signal:l.signal}),fetch("/api/user/custom-roles",{signal:l.signal}),fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({}),signal:l.signal}),fetch("/api/custom-configs/".concat(e,"/default-chat-key"),{signal:l.signal})]),u=null,d=[],h=[],f=[],m=null;if("fulfilled"===r.status&&r.value.ok&&(u=(await r.value.json()).find(t=>t.id===e)),"fulfilled"===o.status&&o.value.ok&&(d=await o.value.json()),"fulfilled"===a.status&&a.value.ok&&(h=await a.value.json()),"fulfilled"===i.status&&i.value.ok&&(f=(await i.value.json()).models||[]),"fulfilled"===s.status&&s.value.ok){let e=await s.value.json();m=(null==e?void 0:e.id)||null}let O={configDetails:u,apiKeys:d,userCustomRoles:h,models:f,defaultChatKeyId:m};return n[e]={data:O,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),O}catch(r){if("AbortError"===r.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[o,a]),c=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>({onMouseEnter:()=>{o(e)||i(e,"high")}}),[i,o]),l=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{delete n[e],t(t=>{let r={...t};return delete r[e],r})},[]),s=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchManageKeysData:i,getCachedData:a,isCached:o,createHoverPrefetch:c,clearCache:l,clearAllCache:s,getStatus:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(t=>e[t]||"idle",[e]),getCacheInfo:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},34962:(e,t,r)=>{r.d(t,{rT:()=>i});var n=r(35695);!function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let o={"/dashboard":{title:"Dashboard",subtitle:"Overview & analytics"},"/playground":{title:"Playground",subtitle:"Test your models",parent:"/dashboard"},"/my-models":{title:"My Models",subtitle:"API key management",parent:"/dashboard"},"/routing-setup":{title:"Routing Setup",subtitle:"Configure routing",parent:"/dashboard"},"/logs":{title:"Logs",subtitle:"Request history",parent:"/dashboard"},"/training":{title:"Prompt Engineering",subtitle:"Custom prompts",parent:"/dashboard"},"/analytics":{title:"Analytics",subtitle:"Advanced insights",parent:"/dashboard"},"/add-keys":{title:"Add Keys",subtitle:"API key setup",parent:"/my-models"}},a=[{pattern:/^\/my-models\/([^\/]+)$/,getConfig:e=>({title:"Manage Keys",subtitle:"API key management",parent:"/my-models"})},{pattern:/^\/routing-setup\/([^\/]+)$/,getConfig:e=>({title:"Routing Configuration",subtitle:"Advanced routing setup",parent:"/routing-setup"})},{pattern:/^\/routing-setup\/([^\/]+)$/,getConfig:e=>({title:"Routing Setup",subtitle:"Advanced configuration",parent:"/routing-setup"})},{pattern:/^\/playground\?config=([^&]+)/,getConfig:(e,t)=>({title:"Playground",subtitle:"Testing configuration",parent:"/playground"})}];function i(){let e=(0,n.usePathname)(),t=(0,n.useSearchParams)(),r=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let r=o[e];if(r)return{title:r.title,subtitle:r.subtitle,parent:r.parent};for(let r of a){let n=e.match(r.pattern);if(n)return r.getConfig(n,t)}let n=e+(t.toString()?"?".concat(t.toString()):"");for(let e of a){let r=n.match(e.pattern);if(r)return e.getConfig(r,t)}return{title:"Dashboard",subtitle:"Overview",parent:void 0}},[e,t]),i=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let t=[];if(r.parent&&o[r.parent]){let e=o[r.parent];t.push({title:e.title,subtitle:e.subtitle,href:r.parent,isActive:!1})}return t.push({title:r.title,subtitle:r.subtitle,href:e,isActive:!0}),t},[r,e]);return{breadcrumb:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>({title:r.title,subtitle:r.subtitle}),[r]),breadcrumbTrail:i,pageTitle:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>"".concat(r.title," - RoKey"),[r]),currentPage:{title:r.title,subtitle:r.subtitle,path:e}}}},37843:(e,t,r)=>{r.d(t,{C:()=>i,e:()=>c});var n=r(35695);!function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();class o{setRouter(e){this.router=e}async prefetchRoute(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.router)return;let{priority:r="low",delay:n=0,condition:o}=t;if(o&&!o())return;let a=this.prefetchedRoutes.get(e);a&&a.prefetched&&Date.now()-a.timestamp<3e5||(this.prefetchQueue.push({route:e,options:t}),this.prefetchedRoutes.set(e,{route:e,timestamp:Date.now(),prefetched:!1}),this.processQueue())}async processQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.length){for(this.isProcessing=!0,this.prefetchQueue.sort((e,t)=>{let r={high:0,low:1};return r[e.options.priority||"low"]-r[t.options.priority||"low"]});this.prefetchQueue.length>0;){let{route:e,options:t}=this.prefetchQueue.shift();try{if(t.delay&&t.delay>0&&await new Promise(e=>setTimeout(e,t.delay)),t.condition&&!t.condition())continue;await this.router.prefetch(e),await this.prefetchBundles(e);let r=this.prefetchedRoutes.get(e);r&&(r.prefetched=!0,r.timestamp=Date.now()),await new Promise(e=>setTimeout(e,50))}catch(e){}}this.isProcessing=!1}}async prefetchBundles(e){try{[].forEach(e=>{let{href:t,as:r,priority:n}=e;if(document.querySelector('link[href="'.concat(t,'"]')))return;let o=document.createElement("link");o.rel="prefetch",o.as=r,o.href=t,o.crossOrigin="anonymous","high"===n&&o.setAttribute("importance","high"),document.head.appendChild(o)}),["https://fonts.googleapis.com","https://fonts.gstatic.com"].forEach(e=>{if(document.querySelector('link[href="'.concat(e,'"][rel="preconnect"]')))return;let t=document.createElement("link");t.rel="preconnect",t.href=e,t.crossOrigin="anonymous",document.head.appendChild(t)})}catch(e){}}cleanup(){let e=Date.now();for(let[t,r]of this.prefetchedRoutes.entries())e-r.timestamp>6e5&&this.prefetchedRoutes.delete(t)}constructor(){this.prefetchedRoutes=new Map,this.router=null,this.prefetchQueue=[],this.isProcessing=!1}}let a=new o,i=()=>{let e=(0,n.useRouter)(),t=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())();Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>(a.setRouter(e),t.current=setInterval(()=>{a.cleanup()},3e5),()=>{t.current&&clearInterval(t.current)}),[e]);let r=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())((e,t)=>{a.prefetchRoute(e,t)},[]),o=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return{onMouseEnter:()=>{r(e,{priority:"high",delay:t})}}},[r]),i=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())((e,t)=>{if(!t)return;let n=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(r(e,{priority:"low",delay:200}),n.disconnect())})},{threshold:.1});return n.observe(t),()=>n.disconnect()},[r]);return{prefetchRoute:r,prefetchOnHover:o,prefetchOnVisible:i}},c=()=>{let{prefetchRoute:e}=i(),t=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({lastActivity:Date.now(),isIdle:!1,mouseMovements:0});return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e,r,n=()=>{t.current.lastActivity=Date.now(),t.current.isIdle=!1,clearTimeout(e),e=setTimeout(()=>{t.current.isIdle=!0},3e3)},o=()=>{t.current.mouseMovements++,n()},a=()=>{n()};return document.addEventListener("mousemove",o),document.addEventListener("keypress",a),document.addEventListener("click",n),document.addEventListener("scroll",n),r=setInterval(()=>{t.current.mouseMovements=0},1e4),()=>{document.removeEventListener("mousemove",o),document.removeEventListener("keypress",a),document.removeEventListener("click",n),document.removeEventListener("scroll",n),clearTimeout(e),clearInterval(r)}},[]),{prefetchWhenIdle:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(r=>{let n=setInterval(()=>{t.current.isIdle&&t.current.mouseMovements<5&&r.forEach((r,n)=>{e(r,{priority:"low",delay:500*n,condition:()=>t.current.isIdle})})},2e3);return()=>clearInterval(n)},[e]),isUserIdle:()=>t.current.isIdle}}},38456:(e,t,r)=>{r.d(t,{Il:()=>d,Rf:()=>s,aU:()=>u,gI:()=>o});let n=e=>![/default_key/i,/attempt_\d+/i,/status_\d+/i,/failed/i,/success/i,/complexity_rr/i,/fallback_position/i,/^[a-f0-9-]{8,}/i,/_then_/i,/classification_/i,/no_prompt/i,/error/i].some(t=>t.test(e))&&/^[a-z_]+$/i.test(e)&&e.length>2&&e.length<50,o=e=>{if(!e)return{text:"N/A",type:"fallback"};if(n(e))return{text:l(e),type:"role",details:"Role-based routing: ".concat(l(e))};if(e.includes("default_key")&&e.includes("success")){let t=e.match(/attempt_(\d+)/),r=t?parseInt(t[1]):1;return{text:1===r?"Default Key":"Default Key (Attempt ".concat(r,")"),type:"success",details:r>1?"Required ".concat(r," attempts to succeed"):void 0}}if(e.includes("default_key")&&e.includes("failed")){let t=e.match(/attempt_(\d+)/),r=e.match(/status_(\w+)/),n=t?parseInt(t[1]):1,o=r?r[1]:"unknown";return{text:"Failed (Attempt ".concat(n,")"),type:"error",details:"Failed with status: ".concat(o)}}if(e.includes("default_all")&&e.includes("attempts_failed")){let t=e.match(/default_all_(\d+)_attempts/),r=t?parseInt(t[1]):0;return{text:"All Keys Failed (".concat(r," attempts)"),type:"error",details:"Tried ".concat(r," different API keys, all failed")}}if(e.includes("complexity_rr_clsf_")||e.includes("complexity_level_")){let t=e.match(/complexity_rr_clsf_(\d+)_used_lvl_(\d+)/);if(t){let e=t[1],r=t[2];return e===r?{text:"Complexity Level ".concat(r),type:"success",details:"Classified and routed to complexity level ".concat(r)}:{text:"Complexity ".concat(e,"→").concat(r),type:"success",details:"Classified as level ".concat(e,", routed to available level ").concat(r)}}let r=e.match(/complexity_level_(\d+)/);if(r){let e=r[1];return{text:"Complexity Level ".concat(e),type:"success",details:"Routed based on prompt complexity analysis"}}}if(e.includes("fallback_position_")){let t=e.match(/fallback_position_(\d+)/),r=t?parseInt(t[1]):0;return{text:"Fallback Key #".concat(r+1),type:"success",details:"Used fallback key at position ".concat(r+1)}}if(e.includes("intelligent_role_")){let t=e.match(/intelligent_role_(.+)$/),r=t?t[1]:"unknown";return{text:"Smart: ".concat(l(r)),type:"role",details:"AI detected role: ".concat(l(r))}}return a(e)},a=e=>{let t=e.match(/complexity[_\s]*(\d+)/i);if(t){let e=t[1];return{text:"Complexity Level ".concat(e),type:"success",details:"Extracted complexity level ".concat(e," from routing pattern")}}let r=i(e);if(r)return{text:l(r),type:"role",details:"Extracted role: ".concat(l(r))};let n=e.match(/fallback[_\s]*(\d+)/i);if(n){let e=parseInt(n[1]);return{text:"Fallback Key #".concat(e+1),type:"success",details:"Extracted fallback position ".concat(e+1)}}let o=e.match(/attempt[_\s]*(\d+)/i);if(o){let t=parseInt(o[1]),r=e.toLowerCase().includes("success"),n=e.toLowerCase().includes("fail");if(r)return{text:1===t?"Default Key":"Default Key (Attempt ".concat(t,")"),type:"success",details:"Extracted success on attempt ".concat(t)};if(n)return{text:"Failed (Attempt ".concat(t,")"),type:"error",details:"Extracted failure on attempt ".concat(t)}}return{text:c(e),type:"fallback",details:"Raw routing pattern: ".concat(e)}},i=e=>{let t=e.match(/classified_as_([a-z_]+)_/i);if(t&&n(t[1]))return t[1];let r=e.match(/role_([a-z_]+)_/i);if(r&&n(r[1]))return r[1];let o=e.match(/fb_role_([a-z_]+)/i);return o&&n(o[1])?o[1]:null},c=e=>{let t=e.replace(/^default_key_[a-f0-9-]+_/i,"").replace(/_attempt_\d+$/i,"").replace(/_status_\w+$/i,"").replace(/_key_selected$/i,"").replace(/_then_.*$/i,"").replace(/^complexity_rr_/i,"").replace(/_no_.*$/i,"");return t&&t.length>2&&t.length<30&&n(t)?l(t):e.replace(/_/g," ").replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ").substring(0,30)+(e.length>30?"...":"")},l=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),s=e=>{switch(e){case"role":return"px-2 py-1 bg-blue-100 text-blue-800 rounded-lg text-xs font-medium";case"success":return"px-2 py-1 bg-green-100 text-green-800 rounded-lg text-xs font-medium";case"error":return"px-2 py-1 bg-red-100 text-red-800 rounded-lg text-xs font-medium";default:return"px-2 py-1 bg-orange-100 text-orange-800 rounded-lg text-xs font-medium"}},u=e=>e?({openai:"OpenAI",anthropic:"Anthropic",google:"Google",openrouter:"OpenRouter",deepseek:"DeepSeek",xai:"xAI"})[e.toLowerCase()]||e:"N/A",d=e=>e?e.replace(/^(gpt-|claude-|gemini-|meta-llama\/|deepseek-|grok-)/,"").replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"N/A"},41e3:(e,t,r)=>{r.d(t,{n4:()=>a,w6:()=>o}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let n={initializing:50,analyzing:150,routing:200,complexity_analysis:250,role_classification:300,preparing:150,connecting:200,generating:400,typing:0,finalizing:100,complete:0};function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{enableAutoProgression:t=!0,stageDurations:r={},onStageChange:o}=e,[a,i]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("initializing"),[c,l]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[s,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),d=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(0),h=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]);({...n,...r});let f=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{h.current.forEach(e=>clearTimeout(e)),h.current=[]},[]),m=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=Date.now();if("connecting"===e){i(e),u(t=>[...t,{stage:e,timestamp:r}]),null==o||o(e,r);let t=setTimeout(()=>{i("routing"),u(e=>[...e,{stage:"routing",timestamp:Date.now()}]),null==o||o("routing",Date.now())},2e3);h.current.push(t);return}t&&f(),i(e),u(t=>[...t,{stage:e,timestamp:r}]),null==o||o(e,r)},[o,f]),O=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e;l(!0),d.current=Date.now(),u([{stage:"initializing",timestamp:Date.now()}]),i("initializing");let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,r=(Math.random()-.5)*2*(t/100*e);return Math.max(200,Math.round(e+r))},r=setTimeout(()=>{m("analyzing",!1)},e=0+t(900,35)),n=setTimeout(()=>{m("complexity_analysis",!1)},e+=t(1200,40)),o=setTimeout(()=>{m("role_classification",!1)},e+=t(1500,35)),a=setTimeout(()=>{m("preparing",!1)},e+=t(1e3,40)),c=setTimeout(()=>{m("connecting",!1)},e+=t(1200,35)),s=setTimeout(()=>{m("routing",!1)},e+=t(1500,40)),f=setTimeout(()=>{m("generating",!1)},e+=t(1200,35));h.current.push(r,n,o,a,c,s,f)},[o,m]),g=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{f();let e=Date.now();i("typing"),u(t=>[...t,{stage:"typing",timestamp:e}]),null==o||o("typing",e)},[f,o]),p=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{f(),m("complete"),l(!1)},[f,m]),_=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{f();let e=Date.now();i("generating"),u(t=>[...t,{stage:"generating",timestamp:e}]),null==o||o("generating",e)},[f,o]),v=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{},[]),E=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{f(),i("initializing"),l(!1),u([]),d.current=0},[f]),D=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>0===d.current?0:Date.now()-d.current,[]);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>()=>{f()},[f]),{currentStage:a,isActive:c,stageHistory:s,startProcessing:O,updateStage:m,markStreaming:g,markComplete:p,markOrchestrationStarted:_,updateOrchestrationStatus:v,reset:E,getProcessingDuration:D}}(e),[r,o]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(new Set),a=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{e.get("x-rokey-role-used"),e.get("x-rokey-routing-strategy"),e.get("x-rokey-complexity-level"),e.get("x-rokey-api-key-provider"),t.updateStage("generating")},[t]),i=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{e.includes("[Complexity Classification]")&&!r.has("complexity")&&(t.updateStage("complexity_analysis"),o(e=>new Set([...e,"complexity"]))),e.includes("[Intelligent Role Strategy]")&&!r.has("role")&&(t.updateStage("role_classification"),o(e=>new Set([...e,"role"]))),e.includes("FIRST TOKEN:")&&!r.has("streaming")&&(t.markStreaming(),o(e=>new Set([...e,"streaming"])))},[t,r]),c=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t.reset(),o(new Set)},[t]);return{...t,analyzeResponseHeaders:a,analyzeStreamChunk:i,reset:c,detectedStages:Array.from(r)}}function a(e){if(!(e.length<2)){for(let t=1;t<e.length;t++){let r=e[t],n=e[t-1];r.timestamp,n.timestamp}e[e.length-1].timestamp,e[0].timestamp}}},42126:(e,t,r)=>{r.d(t,{v:()=>c}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(35695),o=r(42724),a=r(37843);let i={maxConcurrent:3,idleTimeout:2e3,hoverDelay:100,backgroundDelay:5e3};function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,n.usePathname)();(0,n.useRouter)();let{predictions:r,isLearning:c}=(0,o.x)(),{prefetchRoute:l}=(0,a.C)(),s={...i,...e},u=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),d=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(new Set),h=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),f=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e={immediate:[],onIdle:[],onHover:[],background:[]};switch(t){case"/dashboard":e.immediate=["/playground"],e.onIdle=["/my-models","/logs"],e.background=["/routing-setup","/analytics"];break;case"/my-models":e.immediate=["/playground","/routing-setup"],e.onIdle=["/logs"],e.background=["/dashboard","/analytics"];break;case"/playground":e.immediate=["/logs"],e.onIdle=["/my-models"],e.background=["/dashboard","/training"];break;case"/logs":e.immediate=["/playground"],e.onIdle=["/analytics"],e.background=["/my-models","/dashboard"];break;case"/routing-setup":e.immediate=["/playground"],e.onIdle=["/my-models"],e.background=["/logs","/dashboard"];break;default:e.onIdle=["/dashboard","/playground"]}return c&&r.length>0&&(r.slice(0,2).forEach(t=>{e.immediate.includes(t)||e.immediate.unshift(t)}),r.slice(2).forEach(t=>{e.onIdle.includes(t)||e.onIdle.push(t)})),Object.keys(e).forEach(r=>{e[r]=e[r].filter(e=>e!==t)}),e},[t,r,c]),m=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(d.current.has(e)||d.current.size>=s.maxConcurrent)return void u.current.push(e);d.current.add(e);try{await l(e,{priority:t,delay:"high"===t?0:"medium"===t?100:300})}catch(e){}finally{if(d.current.delete(e),u.current.length>0){let e=u.current.shift();e&&setTimeout(()=>m(e,"low"),100)}}},[l,s.maxConcurrent]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{f().immediate.forEach((e,t)=>{setTimeout(()=>{m(e,"high")},50*t)})},[t,f,m]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=f();return h.current&&cancelIdleCallback(h.current),h.current=requestIdleCallback(()=>{e.onIdle.forEach((e,t)=>{setTimeout(()=>{m(e,"medium")},200*t)})},{timeout:s.idleTimeout}),()=>{h.current&&cancelIdleCallback(h.current)}},[t,f,m,s.idleTimeout]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=f(),t=setTimeout(()=>{e.background.forEach((e,t)=>{setTimeout(()=>{m(e,"low")},500*t)})},s.backgroundDelay);return()=>clearTimeout(t)},[t,f,m,s.backgroundDelay]);let O=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>({onMouseEnter:()=>{setTimeout(()=>{m(e,"high")},s.hoverDelay)}}),[m,s.hoverDelay]);return{preloadRoute:m,createHoverPreloader:O,getStatus:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>({activePreloads:Array.from(d.current),queuedPreloads:[...u.current],strategy:f()}),[f]),clearPreloading:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{d.current.clear(),u.current=[],h.current&&(cancelIdleCallback(h.current),h.current=null)},[]),isPreloading:d.current.size>0}}},42724:(e,t,r)=>{r.d(t,{G:()=>c,x:()=>i}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(35695),o=r(37843);let a="rokey_navigation_patterns";function i(){let[e,t]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[r,i]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),c=(0,n.usePathname)(),{prefetchRoute:l}=(0,o.C)(),s=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(Date.now()),u=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(Date.now());function d(){let e=new Date().getHours();return e>=6&&e<12?"morning":e>=12&&e<17?"afternoon":e>=17&&e<21?"evening":"night"}Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=localStorage.getItem(a);if(e)try{let r=JSON.parse(e);t({...r,sessionStartTime:u.current})}catch(e){}else t({patterns:[],sessionStartTime:u.current,totalNavigations:0,preferredRoutes:[],timeOfDay:d()})},[]);let h=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())((r,n)=>{if(!e||r===n)return;let o=Date.now(),i=o-s.current;s.current=o,t(e=>{if(!e)return null;let t=[...e.patterns],c=t.find(e=>e.from===r&&e.to===n);c?(c.frequency+=1,c.lastUsed=o,c.avgTimeSpent=(c.avgTimeSpent+i)/2):t.push({from:r,to:n,frequency:1,lastUsed:o,avgTimeSpent:i});let l=new Map;t.forEach(e=>{l.set(e.to,(l.get(e.to)||0)+e.frequency)});let s=Array.from(l.entries()).sort((e,t)=>t[1]-e[1]).slice(0,5).map(e=>{let[t]=e;return t}),u={...e,patterns:t,totalNavigations:e.totalNavigations+1,preferredRoutes:s,timeOfDay:d()};try{localStorage.setItem(a,JSON.stringify(u))}catch(e){}return u})},[e]),f=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>e&&c?(d(),[...new Set([...e.patterns.filter(e=>e.from===c&&e.frequency>=2).sort((e,t)=>{let r=e.frequency*(1+(Date.now()-e.lastUsed)/864e5);return t.frequency*(1+(Date.now()-t.lastUsed)/864e5)-r}).slice(0,3).map(e=>e.to),...e.patterns.filter(e=>2>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours())).sort((e,t)=>t.frequency-e.frequency).slice(0,2).map(e=>e.to)])].slice(0,4)):[],[e,c]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(e){let e=f();i(e);let t=setTimeout(()=>{e.forEach((e,t)=>{setTimeout(()=>{l(e,{priority:0===t?"high":"low",delay:200*t})},100*t)})},1e3);return()=>clearTimeout(t)}},[e,c,f,l]);let m=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(c);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{m.current&&m.current!==c&&h(m.current,c),m.current=c},[c,h]);let O=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!e)return[];let t=[];e.preferredRoutes.length>0&&t.push("Most visited: ".concat(e.preferredRoutes[0])),e.totalNavigations>10&&t.push("".concat(e.totalNavigations," total navigations this session"));let r=e.patterns.filter(e=>1>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours()));return r.length>0&&t.push("".concat(r.length," patterns match current time")),t},[e]),g=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{localStorage.removeItem(a),t({patterns:[],sessionStartTime:Date.now(),totalNavigations:0,preferredRoutes:[],timeOfDay:d()}),i([])},[]);return{predictions:r,userBehavior:e,insights:O(),trackNavigation:h,clearPatterns:g,isLearning:null!=e&&!!e.totalNavigations&&e.totalNavigations>5}}function c(){let e=(0,n.usePathname)(),[t,r]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let t=[];switch(e){case"/dashboard":t.push({route:"/playground",reason:"Test your models",priority:"high"},{route:"/my-models",reason:"Manage API keys",priority:"medium"},{route:"/logs",reason:"Check recent activity",priority:"medium"});break;case"/my-models":t.push({route:"/playground",reason:"Test new configuration",priority:"high"},{route:"/routing-setup",reason:"Configure routing",priority:"high"},{route:"/logs",reason:"View API usage",priority:"low"});break;case"/playground":t.push({route:"/logs",reason:"View request details",priority:"medium"},{route:"/my-models",reason:"Switch configuration",priority:"medium"},{route:"/training",reason:"Customize prompts",priority:"low"});break;case"/logs":t.push({route:"/playground",reason:"Test similar requests",priority:"high"},{route:"/analytics",reason:"Detailed analysis",priority:"medium"},{route:"/my-models",reason:"Adjust configuration",priority:"low"})}r(t)},[e]),t}},44042:(e,t,r)=>{function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{enableMonitoring:r=!0,enableMemoryTracking:n=!0,enableBundleAnalysis:o=!1,enableCacheTracking:a=!0,warningThresholds:i={renderTime:100,memoryUsage:0x3200000,bundleSize:1048576}}=t,[c,l]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({renderTime:0}),s=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(0),u=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(0),d=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(0),h=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{r&&(s.current=performance.now())},[r]),f=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!r||!s.current)return;let e=performance.now()-s.current;l(t=>({...t,renderTime:e})),i.renderTime,s.current=0},[e,r,i.renderTime]),m=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!n||!("memory"in performance))return;let e=performance.memory,t={used:e.usedJSHeapSize,total:e.totalJSHeapSize,limit:e.jsHeapSizeLimit};l(e=>({...e,memoryUsage:t})),t.used,i.memoryUsage},[e,n,i.memoryUsage]),O=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!o)return;let e=performance.getEntriesByType("resource"),t=0;e.forEach(e=>{e.name.includes(".js")&&e.transferSize&&(t+=e.transferSize)}),l(e=>({...e,bundleSize:t})),i.bundleSize},[o,i.bundleSize]),g=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!a)return;let e=d.current>0?u.current/d.current*100:0;l(t=>({...t,cacheHitRate:e}))},[a]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!a)return;let e=e=>{var t,r;(null==(t=e.data)?void 0:t.type)==="CACHE_HIT"?(u.current++,d.current++,g()):(null==(r=e.data)?void 0:r.type)==="CACHE_MISS"&&(d.current++,g())};if("serviceWorker"in navigator)return navigator.serviceWorker.addEventListener("message",e),()=>{navigator.serviceWorker.removeEventListener("message",e)}},[a,g]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!r)return;let e=()=>{let e=performance.getEntriesByType("navigation")[0];if(e){let t=e.loadEventEnd-e.navigationStart;l(e=>({...e,navigationTime:t}))}};if("complete"!==document.readyState)return window.addEventListener("load",e),()=>window.removeEventListener("load",e);e()},[r]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!r)return;let e=setInterval(()=>{m(),O()},5e3);return()=>clearInterval(e)},[r,m,O]);let p=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let e=[];return c.renderTime>100&&(e.push("Consider memoizing expensive calculations"),e.push("Use React.memo for component optimization"),e.push("Implement virtualization for large lists")),c.memoryUsage&&c.memoryUsage.used>0x3200000&&(e.push("Check for memory leaks"),e.push("Optimize image sizes and formats"),e.push("Implement proper cleanup in useEffect")),c.bundleSize&&c.bundleSize>1048576&&(e.push("Implement code splitting"),e.push("Use dynamic imports for heavy components"),e.push("Remove unused dependencies")),void 0!==c.cacheHitRate&&c.cacheHitRate<70&&(e.push("Improve caching strategy"),e.push("Implement service worker caching"),e.push("Use browser cache headers")),e},[c]),_=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>({component:e,timestamp:new Date().toISOString(),metrics:c,suggestions:p(),userAgent:navigator.userAgent,url:window.location.href}),[e,c,p]);return{metrics:c,startMeasurement:h,endMeasurement:f,trackMemoryUsage:m,analyzeBundleSize:O,trackCacheHitRate:g,getOptimizationSuggestions:p,exportMetrics:_}}r.d(t,{D:()=>n}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},53951:(e,t,r)=>{r.d(t,{c:()=>o}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let n={};function o(){let[e,t]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({}),r=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({}),o=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),a=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),i=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async function(e){var i;let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"medium";if(o(e))return a(e);if(null==(i=n[e])?void 0:i.isLoading)return null;r.current[e]&&r.current[e].abort();let l=new AbortController;r.current[e]=l,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===c?await new Promise(e=>setTimeout(e,200)):"medium"===c&&await new Promise(e=>setTimeout(e,50));let[r,o,a]=await Promise.allSettled([fetch("/api/custom-configs/".concat(e),{signal:l.signal}),fetch("/api/keys?custom_config_id=".concat(e),{signal:l.signal}),fetch("/api/complexity-assignments?custom_config_id=".concat(e),{signal:l.signal})]),i=null,s=[],u="none",d={},h=[];"fulfilled"===r.status&&r.value.ok&&(u=(i=await r.value.json()).routing_strategy||"none",d=i.routing_strategy_params||{}),"fulfilled"===o.status&&o.value.ok&&(s=await o.value.json()),"fulfilled"===a.status&&a.value.ok&&(h=await a.value.json());let f={configDetails:i,apiKeys:s,routingStrategy:u,routingParams:d,complexityAssignments:h};return n[e]={data:f,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),f}catch(r){if("AbortError"===r.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[o,a]),c=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>({onMouseEnter:()=>{o(e)||i(e,"high")}}),[i,o]),l=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{delete n[e],t(t=>{let r={...t};return delete r[e],r})},[]),s=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchRoutingSetupData:i,getCachedData:a,isCached:o,createHoverPrefetch:c,clearCache:l,clearAllCache:s,getStatus:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(t=>e[t]||"idle",[e]),getCacheInfo:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},70036:(e,t,r)=>{function n(e,t){let[r,n]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[o,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[i,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[l,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[u,d]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),h=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),f=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null);let m=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(0),O=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),g=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),p=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{h.current&&(h.current.close(),h.current=null),f.current&&(clearTimeout(f.current),f.current=null),a(!1)},[]),_=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!e&&!t)return void c("No execution ID or direct stream URL provided");let r=t||(e?"/api/orchestration/stream/".concat(e):"");if(!r)return void c("No valid stream URL could be determined");if(g.current!==r||!o){p(),O.current=e||"",g.current=r;try{let e=new EventSource(r);h.current=e,e.onopen=()=>{a(!0),c(null),m.current=0},e.onmessage=e=>{try{let t=JSON.parse(e.data);n(e=>[...e,t]),s(t),c(null)}catch(e){c("Error parsing stream data")}},e.addEventListener("orchestration_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_progress",e=>{JSON.parse(e.data)}),e.addEventListener("step_completed",e=>{JSON.parse(e.data)}),e.addEventListener("synthesis_started",e=>{JSON.parse(e.data)}),e.addEventListener("orchestration_completed",e=>{JSON.parse(e.data)}),e.onerror=e=>{if(a(!1),m.current<5){let e=1e3*Math.pow(2,m.current);m.current++,c("Connection lost. Reconnecting in ".concat(e/1e3,"s... (attempt ").concat(m.current,"/").concat(5,")")),f.current=setTimeout(()=>{_()},e)}else c("Connection failed after multiple attempts. Please refresh the page.")}}catch(e){c("Failed to establish connection"),a(!1)}}},[e,p]),v=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{m.current=0,_()},[_]);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>((e||t)&&_(),()=>{p()}),[e,t,_,p]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let r=()=>{"visible"===document.visibilityState&&!o&&(e||t)&&v()};return document.addEventListener("visibilitychange",r),()=>{document.removeEventListener("visibilitychange",r)}},[o,e,t,v]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{let r=()=>{!o&&(e||t)&&v()},n=()=>{c("Network connection lost")};return window.addEventListener("online",r),window.addEventListener("offline",n),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",n)}},[o,e,t,v]),{events:r,isConnected:o,error:i,lastEvent:l,reconnect:v,disconnect:p}}r.d(t,{LJ:()=>n}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},71118:(e,t,r)=>{r.d(t,{j:()=>a}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=r(35695);let o=["/features","/pricing","/about","/auth/signin","/auth/signup","/docs"];function a(){let e=(0,n.useRouter)();return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{(async()=>{"requestIdleCallback"in window?window.requestIdleCallback(()=>{o.forEach(t=>{e.prefetch(t)})}):setTimeout(()=>{o.forEach(t=>{e.prefetch(t)})},100)})()},[e]),{navigateInstantly:Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(t=>{Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{e.push(t)})},[e])}}},87162:(e,t,r)=>{function n(){let[e,t]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),r=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())((e,r)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await r(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),n=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:r,hideConfirmation:n}}r.d(t,{Z:()=>n}),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},96121:()=>{class e{static getInstance(){return e.instance||(e.instance=new e),e.instance}trackParallelFlow(e){let t="".concat(e.provider,"_").concat(e.model,"_parallel");this.parallelMetrics||(this.parallelMetrics=new Map),this.parallelMetrics.has(t)||this.parallelMetrics.set(t,[]);let r=this.parallelMetrics.get(t);r.push({...e,timestamp:Date.now()}),r.length>this.maxSamples&&r.shift(),e.firstTokenTime}trackMessagingFlow(e){let t="".concat(e.provider,"_").concat(e.model);this.metrics.has(t)||this.metrics.set(t,[]);let r=this.metrics.get(t);r.push({timestamp:Date.now(),...e}),r.length>this.maxSamples&&r.shift(),this.logPerformanceInsights(e)}getStats(e,t){let r="".concat(e,"_").concat(t),n=this.metrics.get(r);if(!n||0===n.length)return null;let o=n.filter(e=>e.success);if(0===o.length)return null;let a=o.map(e=>e.timings.total),i=o.map(e=>e.timings.llmApiCall),c=o.map(e=>e.messageLength);return{provider:e,model:t,sampleCount:o.length,averageTotal:this.calculateAverage(a),averageLLM:this.calculateAverage(i),medianTotal:this.calculateMedian(a),medianLLM:this.calculateMedian(i),p95Total:this.calculatePercentile(a,95),p95LLM:this.calculatePercentile(i,95),minTotal:Math.min(...a),maxTotal:Math.max(...a),averageMessageLength:this.calculateAverage(c),streamingUsage:o.filter(e=>e.isStreaming).length/o.length,errorRate:(n.length-o.length)/n.length,recentTrend:this.calculateTrend(a.slice(-10))}}getSummary(){let e=new Set,t=[];for(let r of this.metrics.keys()){let[n,o]=r.split("_");e.add(n);let a=this.getStats(n,o);a&&t.push(a)}if(0===t.length)return{totalProviders:0,totalModels:0,overallAverageTime:0,fastestProvider:null,slowestProvider:null,recommendations:["No messaging data available yet"]};let r=this.calculateAverage(t.map(e=>e.averageTotal)),n=[...t].sort((e,t)=>e.averageTotal-t.averageTotal);return{totalProviders:e.size,totalModels:t.length,overallAverageTime:r,fastestProvider:n[0],slowestProvider:n[n.length-1],recommendations:this.generateRecommendations(t)}}generateRecommendations(e){let t=[],r=e.filter(e=>e.averageTotal>5e3);r.length>0&&t.push("Consider switching from slow providers: ".concat(r.map(e=>e.provider).join(", "))),e.filter(e=>e.streamingUsage<.5).length>0&&t.push("Enable streaming for better perceived performance");let n=e.filter(e=>e.errorRate>.1);n.length>0&&t.push("High error rates detected for: ".concat(n.map(e=>e.provider).join(", ")));let o=e.filter(e=>e.averageTotal<=2e3);return 0===o.length?t.push("No providers meeting 2s target - consider optimizing or switching providers"):t.push("Fast providers (≤2s): ".concat(o.map(e=>e.provider).join(", "))),t.length>0?t:["Performance looks good!"]}logPerformanceInsights(e){let{provider:t,model:r,timings:n,isStreaming:o,streamingMetrics:a}=e;o&&n.timeToFirstToken&&(n.timeToFirstToken<500||n.timeToFirstToken<1e3||n.timeToFirstToken,a&&a.averageTokenLatency),n.total,n.total,n.total,n.llmApiCall,n.llmApiCall,n.total,n.backendProcessing&&n.frontendProcessing&&(n.backendProcessing,n.total,n.frontendProcessing,n.total),o||n.total}calculateAverage(e){return e.reduce((e,t)=>e+t,0)/e.length}calculateMedian(e){let t=[...e].sort((e,t)=>e-t),r=Math.floor(t.length/2);return t.length%2==0?(t[r-1]+t[r])/2:t[r]}calculatePercentile(e,t){let r=[...e].sort((e,t)=>e-t),n=Math.ceil(t/100*r.length)-1;return r[Math.max(0,n)]}calculateTrend(e){if(e.length<5)return"stable";let t=e.slice(0,Math.floor(e.length/2)),r=e.slice(Math.floor(e.length/2)),n=this.calculateAverage(t),o=(this.calculateAverage(r)-n)/n*100;return o<-10?"improving":o>10?"degrading":"stable"}exportData(){let e={};for(let[t,r]of this.metrics.entries())e[t]=[...r];return e}clear(){this.metrics.clear()}constructor(){this.metrics=new Map,this.maxSamples=50,this.parallelMetrics=new Map}}class t{static getInstance(){return t.instance||(t.instance=new t),t.instance}startRequest(e,t,r){this.timingData.set(e,{requestStart:performance.now(),tokenCount:0,provider:t,model:r})}markFirstToken(e){let t=this.timingData.get(e);if(!t)return null;let r=performance.now()-t.requestStart;return t.firstTokenReceived=performance.now(),r}trackToken(e){let t=this.timingData.get(e);t&&t.tokenCount++}completeStream(e){let t=this.timingData.get(e);if(!t||!t.firstTokenReceived)return null;let r=performance.now();t.streamComplete=r;let n=t.firstTokenReceived-t.requestStart,o=r-t.requestStart,a=r-t.firstTokenReceived,i=t.tokenCount>1?a/(t.tokenCount-1):0,c={timeToFirstToken:n,totalStreamTime:o,totalTokens:t.tokenCount,averageTokenLatency:i};return this.timingData.delete(e),c}getStatus(e){let t=this.timingData.get(e);return t?t.firstTokenReceived?t.streamComplete?"Complete":"Streaming in progress":"Waiting for first token":"Not tracked"}clear(){this.timingData.clear()}constructor(){this.timingData=new Map}}let r=e.getInstance(),n=t.getInstance();function o(){let e=r.getSummary();e.fastestProvider,e.slowestProvider,e.recommendations.forEach(e=>console.log("   • ".concat(e)))}function a(){var e;null==(e=n.timingData)||e.size}function i(){}function c(){let e=setInterval(()=>{r.getSummary().totalProviders},3e4);globalThis.__performanceMonitoringInterval=e}function l(){let e=globalThis.__performanceMonitoringInterval;e&&(clearInterval(e),delete globalThis.__performanceMonitoringInterval)}function s(){let e=r.getSummary();0!==e.totalProviders&&(e.overallAverageTime<2e3||e.overallAverageTime<5e3||e.overallAverageTime,e.fastestProvider,e.slowestProvider)}"undefined"!=typeof globalThis&&(globalThis.logComprehensivePerformanceReport=o,globalThis.logFirstTokenReport=a,globalThis.logGoogleStreamingDebug=i,globalThis.quickPerformanceCheck=s,globalThis.startPerformanceMonitoring=c,globalThis.stopPerformanceMonitoring=l,globalThis.performanceLogs={comprehensive:o,firstToken:a,googleDebug:i,quick:s,startMonitoring:c,stopMonitoring:l})}}]);