(()=>{var e={};e.id=8118,e.ids=[8118],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var s=r(34386),a=r(44999);async function i(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},40768:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{PUT:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(2507);async function p(e){let t=await (0,u.x)();try{let{conversation_id:r,timestamp:s,content:a}=await e.json();if(!r||!s||!a)return o.NextResponse.json({error:"conversation_id, timestamp, and content are required"},{status:400});if(!Array.isArray(a)||0===a.length)return o.NextResponse.json({error:"Content must be a non-empty array"},{status:400});let i=new Date(parseInt(s));if(isNaN(i.getTime()))return o.NextResponse.json({error:"Invalid timestamp format"},{status:400});let n=new Date(i.getTime()-5e3),u=new Date(i.getTime()+5e3),{data:p,error:d}=await t.from("chat_messages").select("id, created_at").eq("conversation_id",r).gte("created_at",n.toISOString()).lte("created_at",u.toISOString()).order("created_at",{ascending:!0});if(d)return o.NextResponse.json({error:"Failed to find message",details:d.message},{status:500});if(!p||0===p.length)return o.NextResponse.json({error:"Message not found at specified timestamp",suggestion:"This might be a new message that needs to be created instead of updated"},{status:404});let c=p[0];p.length>1&&(c=p.reduce((e,t)=>{let r=Math.abs(new Date(e.created_at).getTime()-i.getTime());return Math.abs(new Date(t.created_at).getTime()-i.getTime())<r?t:e}));let{data:m,error:g}=await t.from("chat_messages").update({content:a}).eq("id",c.id).select().single();if(g)return o.NextResponse.json({error:"Failed to update message",details:g.message},{status:500});return await t.from("chat_conversations").update({updated_at:new Date().toISOString()}).eq("id",r),o.NextResponse.json({success:!0,message:`Updated message ${c.id}`,updated_message:m},{status:200})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/chat/messages/update-by-timestamp/route",pathname:"/api/chat/messages/update-by-timestamp",filename:"route",bundlePath:"app/api/chat/messages/update-by-timestamp/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\chat\\messages\\update-by-timestamp\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:m,serverHooks:g}=d;function l(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410],()=>r(40768));module.exports=s})();