import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Rules considered to be best practice for modern TypeScript codebases, but that do not impact program logic.
 * @see {@link https://typescript-eslint.io/users/configs#stylistic}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=stylistic.d.ts.map