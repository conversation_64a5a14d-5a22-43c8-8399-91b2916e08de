"use strict";(()=>{var e={};e.id=8194,e.ids=[8194],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52639:(e,t,o)=>{o.r(t),o.d(t,{patchFetch:()=>b,routeModule:()=>y,serverHooks:()=>k,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var s={};o.r(s),o.d(s,{POST:()=>f});var r=o(96559),a=o(48088),n=o(37719),i=o(32190),p=o(2507),l=o(56534),c=o(45697),d=o(5649),u=o(62854),m=o(68811);async function _(e,t,o=3,s){let r;for(let s=1;s<=o;s++)try{return await fetch(e,t)}catch(t){if(r=t,s===o)throw Error(t.message);let e=100*s;await new Promise(t=>setTimeout(t,e))}throw r}async function g(e,t,o,s,r){let a,n,i,p=new Date,l=new Date;try{let c=function(e,t){if(!e)return"";let o=t.toLowerCase(),s=`${o}/`;return e.toLowerCase().startsWith(s)?e.substring(s.length):e}(t,e||""),d=e?.toLowerCase()==="openrouter"?t:c;if(e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...p}=s,c={...p,model:d,messages:s.messages,stream:s.stream};Object.keys(c).forEach(e=>void 0===c[e]&&delete c[e]);let u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(c)},m=await _("https://api.openai.com/v1/chat/completions",u);if(l=new Date,a=m.status,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw n={message:`OpenAI Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(s.stream&&m.body&&r){let e=m.body.getReader(),t=new TextDecoder,o="",s="";try{for(;;){let{done:a,value:n}=await e.read();if(a)break;let p=(s+=t.decode(n,{stream:!0})).split("\n");for(let e of(s=p.pop()||"",p))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),s=e.choices?.[0]?.delta?.content||"";s&&(o+=s,r(s)),e.usage&&(i={...i,usage:e.usage,choices:[{message:{content:o},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}i||(i={choices:[{message:{content:o},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else i=await m.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...p}=s,c={...p,model:d,messages:s.messages,stream:s.stream,usage:{include:!0}},u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey Orchestration","User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},m=await _("https://openrouter.ai/api/v1/chat/completions",u);if(l=new Date,a=m.status,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw n={message:`OpenRouter Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(s.stream&&m.body&&r){let e=m.body.getReader(),t=new TextDecoder,o="",s="";try{for(;;){let{done:a,value:n}=await e.read();if(a)break;let p=(s+=t.decode(n,{stream:!0})).split("\n");for(let e of(s=p.pop()||"",p))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),s=e.choices?.[0]?.delta?.content||"";s&&(o+=s,r(s)),e.usage&&(i={...i,usage:e.usage,choices:[{message:{content:o},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}i||(i={choices:[{message:{content:o},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else i=await m.json()}else if(e?.toLowerCase()==="google"){let e=d?.replace(/^models\//,"")||d,t=s.messages.map(e=>{if("string"==typeof e.content)return{role:e.role,content:e.content};if(Array.isArray(e.content)){let t=e.content.map(e=>"text"===e.type&&"string"==typeof e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:null).filter(Boolean);return{role:e.role,content:t}}return{role:e.role,content:"[RoKey: Invalid content structure for Google]"}});if(0===t.length)throw n={message:"No processable message content found for Google provider after filtering.",status:400};let p={model:e,messages:t,stream:s.stream||!1};void 0!==s.temperature&&(p.temperature=s.temperature),void 0!==s.max_tokens&&(p.max_tokens=s.max_tokens),void 0!==s.top_p&&(p.top_p=s.top_p);let c={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,"User-Agent":"RoKey/1.0 (Orchestration)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(p)},u=await _("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",c);if(l=new Date,a=u.status,!u.ok){let e=await u.json().catch(()=>({error:{message:u.statusText}})),t=e?.error?.message||u.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),n=503===u.status||429===u.status||t.includes("overloaded")||t.includes("rate limit")?{message:`Google Error: ${t}`,status:u.status,provider_error:e,retryable:!0}:{message:`Google Error: ${t}`,status:u.status,provider_error:e}}if(s.stream&&u.body&&r){let e=u.body.getReader(),t=new TextDecoder,o="",s="";try{for(;;){let{done:a,value:n}=await e.read();if(a)break;let p=(s+=t.decode(n,{stream:!0})).split("\n");for(let e of(s=p.pop()||"",p))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),s=e.choices?.[0]?.delta?.content||"";s&&(o+=s,r(s)),e.usage&&(i={...i,usage:e.usage,choices:[{message:{content:o},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}i||(i={choices:[{message:{content:o},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else i=await u.json()}else if(e?.toLowerCase()==="anthropic"){let e,t=s.max_tokens||2048,p=s.messages.filter(t=>"system"!==t.role||("string"==typeof t.content&&(e=t.content),!1)).map(e=>({role:e.role,content:e.content}));if(0===p.length||"user"!==p[0].role)throw n={message:"Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.",status:400};let c={model:d,messages:p,max_tokens:t,stream:s.stream};e&&(c.system=e),void 0!==s.temperature&&(c.temperature=s.temperature),void 0!==s.top_p&&(c.top_p=s.top_p);let u={method:"POST",headers:{"Content-Type":"application/json","x-api-key":o,"anthropic-version":"2023-06-01","User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},m=await _("https://api.anthropic.com/v1/messages",u);if(l=new Date,a=m.status,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw n={message:`Anthropic Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(s.stream&&m.body&&r){let e=m.body.getReader(),t=new TextDecoder,o="",s="";try{for(;;){let{done:a,value:n}=await e.read();if(a)break;let p=(s+=t.decode(n,{stream:!0})).split("\n");for(let e of(s=p.pop()||"",p))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),s=e.delta?.text||"";s&&(o+=s,r(s)),e.usage&&(i={id:e.id,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:d,choices:[{index:0,message:{role:"assistant",content:o},finish_reason:e.delta?.stop_reason||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens||0,completion_tokens:e.usage?.output_tokens||0,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}})}catch(e){}}}}finally{e.releaseLock()}i||(i={id:`anthropic-${Date.now()}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:d,choices:[{index:0,message:{role:"assistant",content:o},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else{let e=await m.json(),t=e.content?.[0]?.text||"";i={id:e.id,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:d,choices:[{index:0,message:{role:"assistant",content:t},finish_reason:e.stop_reason?.toLowerCase()||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens,completion_tokens:e.usage?.output_tokens,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}}}}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...p}=s,c={...p,model:d,messages:s.messages,stream:s.stream};Object.keys(c).forEach(e=>void 0===c[e]&&delete c[e]);let u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,"User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},m=await _("https://api.deepseek.com/v1/chat/completions",u);if(l=new Date,a=m.status,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw n={message:`DeepSeek Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(s.stream&&m.body&&r){let e=m.body.getReader(),t=new TextDecoder,o="",s="";try{for(;;){let{done:a,value:n}=await e.read();if(a)break;let p=(s+=t.decode(n,{stream:!0})).split("\n");for(let e of(s=p.pop()||"",p))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),s=e.choices?.[0]?.delta?.content||"";s&&(o+=s,r(s)),e.usage&&(i={...i,usage:e.usage,choices:[{message:{content:o},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}i||(i={choices:[{message:{content:o},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else i=await m.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...p}=s,c={...p,model:d,messages:s.messages,stream:s.stream||!1};"number"==typeof s.temperature&&(c.temperature=s.temperature),"number"==typeof s.max_tokens&&(c.max_tokens=s.max_tokens),"number"==typeof s.top_p&&(c.top_p=s.top_p);let u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,"User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},m=await _("https://api.x.ai/v1/chat/completions",u);if(l=new Date,a=m.status,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw n={message:`XAI Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(s.stream&&m.body&&r){let e=m.body.getReader(),t=new TextDecoder,o="",s="";try{for(;;){let{done:a,value:n}=await e.read();if(a)break;let p=(s+=t.decode(n,{stream:!0})).split("\n");for(let e of(s=p.pop()||"",p))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),s=e.choices?.[0]?.delta?.content||"";s&&(o+=s,r(s)),e.usage&&(i={...i,usage:e.usage,choices:[{message:{content:o},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}i||(i={choices:[{message:{content:o},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else i=await m.json()}else throw Error(`Unsupported provider: ${e}`);return{success:!0,response:void 0,responseData:i,status:a,error:n,llmRequestTimestamp:p,llmResponseTimestamp:l}}catch(e){return{success:!1,response:void 0,responseData:void 0,status:a||500,error:e,llmRequestTimestamp:p,llmResponseTimestamp:l}}}let h=c.z.object({executionId:c.z.string().uuid(),stepId:c.z.string().uuid()});async function f(e){let t=(0,p.x)();try{let o,s=await e.json(),r=h.parse(s),{data:a,error:n}=await t.from("orchestration_steps").select("*").eq("id",r.stepId).eq("execution_id",r.executionId).single();if(n||!a)return i.NextResponse.json({error:"Step not found"},{status:404});if("pending"!==a.status)return i.NextResponse.json({error:`Step is in ${a.status} state, not pending`},{status:400});let p=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!p)return i.NextResponse.json({error:"Classification API key not configured"},{status:500});let c=new d.y(p,r.executionId);await t.from("orchestration_steps").update({status:"in_progress",started_at:new Date().toISOString()}).eq("id",a.id);let _={id:crypto.randomUUID(),execution_id:r.executionId,type:"moderator_assignment",timestamp:new Date().toISOString(),data:{message:(0,m.re)("moderator_assignment",a.role_id),step:{number:a.step_number,role:a.role_id,model:a.model_name}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,_),await new Promise(e=>setTimeout(e,1e3));let f={id:crypto.randomUUID(),execution_id:r.executionId,type:"specialist_acknowledgment",timestamp:new Date().toISOString(),data:{message:(0,m.re)("specialist_acknowledgment",a.role_id),step:{number:a.step_number,role:a.role_id,model:a.model_name}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,f),await new Promise(e=>setTimeout(e,800));let y={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_started",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("step_started",{roleId:a.role_id}),step:{number:a.step_number,role:a.role_id,model:a.model_name,estimatedDuration:45e3}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};if((0,u.broadcastOrchestrationEvent)(r.executionId,y),!a.api_key_id)return await t.from("orchestration_steps").update({status:"failed",error_message:"No API key assigned to this step"}).eq("id",a.id),i.NextResponse.json({error:"No API key assigned to this step"},{status:400});let{data:x,error:w}=await t.from("api_keys").select("*").eq("id",a.api_key_id).single();if(w||!x)return await t.from("orchestration_steps").update({status:"failed",error_message:"API key not found"}).eq("id",a.id),i.NextResponse.json({error:"API key not found"},{status:404});let k=(0,l.Y)(x.encrypted_api_key),b=a.prompt;if(b.includes("{{previousOutput}}")){let{data:e}=await t.from("orchestration_steps").select("response").eq("execution_id",a.execution_id).eq("step_number",a.step_number-1).single();b=e&&e.response?b.replace("{{previousOutput}}",e.response):b.replace("{{previousOutput}}","No previous output available")}let v=Date.now(),I=0,O=0,D=0;try{let e,t,s={custom_api_config_id:a.execution_id,messages:[{role:"user",content:b}],stream:!0,temperature:.7,max_tokens:2e3},n={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("step_progress",{roleId:a.role_id}),progress:.3,status:"Executing model call..."},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,n);let i="",p=e=>{i+=e;let t={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_streaming",timestamp:new Date().toISOString(),data:{commentary:`💭 ${a.role_id} is thinking...`,progress:Math.min(80,i.length/500*100),status:"Streaming response...",partialResponse:i,deltaContent:e},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,t)};for(let o=1;o<=3;o++)try{if((e=await g(x.provider,x.predefined_model_id,k,s,p)).success)break;if(t=e.error,e.error?.retryable&&o<3){let t=Math.min(1e3*Math.pow(2,o-1),1e4),s={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:`🔄 ${a.role_id} encountered temporary overload. Retrying in ${Math.round(t/1e3)}s... (attempt ${o}/3)`,progress:.2,status:`Retrying due to: ${e.error.message}`},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,s),await new Promise(e=>setTimeout(e,t));continue}throw Error(e.error?.message||"Provider request failed")}catch(s){if(t=s,3===o)throw s;let e=Math.min(1e3*Math.pow(2,o-1),1e4);await new Promise(t=>setTimeout(t,e))}if(!e||!e.success)throw Error(t?.message||"All retry attempts failed");if(e.responseData?.choices?.[0]?.message?.content)o=e.responseData.choices[0].message.content;else if(e.responseData?.content?.[0]?.text)o=e.responseData.content[0].text;else throw Error("No valid response content found");I=e.responseData?.usage?.prompt_tokens||e.responseData?.usage?.input_tokens||0,O=e.responseData?.usage?.completion_tokens||e.responseData?.usage?.output_tokens||0,D=1e-6*I+2e-6*O}catch(o){await t.from("orchestration_steps").update({status:"failed",completed_at:new Date().toISOString(),duration_ms:Date.now()-v,error_message:o instanceof Error?o.message:"Unknown error"}).eq("id",a.id);let e={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_failed",timestamp:new Date().toISOString(),data:{commentary:`❌ ${a.role_id} encountered an issue. Analyzing recovery options...`,error:o instanceof Error?o.message:"Unknown error",retryPlan:"Manual intervention required"},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};return(0,u.broadcastOrchestrationEvent)(r.executionId,e),i.NextResponse.json({error:"Model execution failed",details:o instanceof Error?o.message:"Unknown error"},{status:500})}let S=Date.now()-v,E=await c.validateStepOutput(a.step_number,a.role_id,o,b,`Expected output for ${a.role_id} role`);await t.from("orchestration_steps").update({status:"completed",completed_at:new Date().toISOString(),duration_ms:S,tokens_in:I,tokens_out:O,cost:D,response:o,prompt:b}).eq("id",a.id);let T={id:crypto.randomUUID(),execution_id:r.executionId,type:"specialist_message",timestamp:new Date().toISOString(),data:{message:(0,m.re)("specialist_message",a.role_id),output:o.length>200?o.substring(0,200)+"...":o,fullOutput:o,duration:S,quality:E.quality},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,T),await new Promise(e=>setTimeout(e,1200));let q={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_completed",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("step_completed",{roleId:a.role_id}),output:o,duration:S,tokens:{input:I,output:O},cost:D,quality:E.quality,validation:E},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(r.executionId,q);let{data:R}=await t.from("orchestration_executions").select("total_steps, created_at").eq("id",a.execution_id).single();if(R&&a.step_number===R.total_steps){let{data:e}=await t.from("orchestration_steps").select("step_number, role_id, response, prompt").eq("execution_id",a.execution_id).eq("status","completed").order("step_number",{ascending:!0});if(e&&e.length>0){let t=`/api/orchestration/synthesis-stream/${a.execution_id}`,o={id:crypto.randomUUID(),execution_id:a.execution_id,type:"synthesis_started",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("synthesis_started",{totalSteps:e.length}),steps:e.length,status:"Synthesizing all specialist outputs...",directStreamUrl:t,synthesisExecutionId:a.execution_id}};(0,u.broadcastOrchestrationEvent)(a.execution_id,o)}else await t.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString()}).eq("id",a.execution_id)}else if(R){await t.from("orchestration_steps").update({status:"pending"}).eq("execution_id",a.execution_id).eq("step_number",a.step_number+1);let{data:e}=await t.from("orchestration_steps").select("*").eq("execution_id",a.execution_id).eq("step_number",a.step_number+1).single();if(e){let t={id:crypto.randomUUID(),execution_id:a.execution_id,type:"handoff_message",timestamp:new Date().toISOString(),data:{message:(0,m.re)("handoff_message",a.role_id),fromRole:a.role_id,toRole:e.role_id,nextStep:{number:e.step_number,role:e.role_id,model:e.model_name}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.broadcastOrchestrationEvent)(a.execution_id,t),setTimeout(()=>{fetch(`${process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"}/api/orchestration/process-step`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({executionId:a.execution_id,stepId:e.id})}).catch(e=>{})},1500)}}return i.NextResponse.json({success:!0,step:{id:a.id,status:"completed",response:o,duration:S,tokens:{input:I,output:O},cost:D,quality:E.quality}})}catch(e){return i.NextResponse.json({error:"Internal server error",details:String(e)},{status:500})}}let y=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orchestration/process-step/route",pathname:"/api/orchestration/process-step",filename:"route",bundlePath:"app/api/orchestration/process-step/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\process-step\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:k}=y;function b(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56534:(e,t,o)=>{o.d(t,{Y:()=>l,w:()=>p});var s=o(55511),r=o.n(s);let a="aes-256-gcm",n=process.env.ROKEY_ENCRYPTION_KEY;if(!n||64!==n.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let i=Buffer.from(n,"hex");function p(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=r().randomBytes(12),o=r().createCipheriv(a,i,t),s=o.update(e,"utf8","hex");s+=o.final("hex");let n=o.getAuthTag();return`${t.toString("hex")}:${n.toString("hex")}:${s}`}function l(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let o=Buffer.from(t[0],"hex"),s=Buffer.from(t[1],"hex"),n=t[2];if(12!==o.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==s.length)throw Error("Invalid authTag length. Expected 16 bytes.");let p=r().createDecipheriv(a,i,o);p.setAuthTag(s);let l=p.update(n,"hex","utf8");return l+p.final("utf8")}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410,5697,7266],()=>o(52639));module.exports=s})();