"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/app/auth/signin/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signin/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignInPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SignInPage() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        try {\n            // TODO: Implement Supabase authentication\n            console.log('Sign in with:', {\n                email,\n                password\n            });\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Redirect to dashboard on success\n            window.location.href = '/dashboard';\n        } catch (err) {\n            setError('Invalid email or password. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setIsLoading(true);\n        try {\n            // TODO: Implement Google OAuth\n            console.log('Sign in with Google');\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Redirect to dashboard on success\n            window.location.href = '/dashboard';\n        } catch (err) {\n            setError('Failed to sign in with Google. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.064,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"radial-gradient(circle, rgba(255, 107, 53, 0.15) 1px, transparent 1px)\",\n                        backgroundSize: '100px 100px',\n                        mask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\",\n                        WebkitMask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 w-full max-w-6xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 opacity-20\",\n                                        style: {\n                                            backgroundImage: \"\\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                  \",\n                                            backgroundSize: '30px 30px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: \"/roukey_logo.png\",\n                                                            alt: \"RouKey\",\n                                                            width: 48,\n                                                            height: 48,\n                                                            className: \"w-12 h-12 object-contain\",\n                                                            priority: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold mb-4\",\n                                                        children: \"Welcome to RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-white/90 mb-8\",\n                                                        children: [\n                                                            \"Access to \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: \"UNLIMITED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            \" AI requests across 300+ models\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Intelligent Role Routing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Enterprise Security\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"No Request Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"w-full max-w-md mx-auto lg:mx-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: \"/roukey_logo.png\",\n                                                        alt: \"RouKey\",\n                                                        width: 40,\n                                                        height: 40,\n                                                        className: \"w-full h-full object-contain\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-black\",\n                                                    children: \"RouKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold text-black mb-3\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-lg\",\n                                            children: \"Welcome back to your AI gateway\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-5\",\n                                            style: {\n                                                backgroundImage: \"\\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                  \",\n                                                backgroundSize: '20px 20px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-600 text-sm\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSubmit,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDCE7 Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"email\",\n                                                                    name: \"email\",\n                                                                    type: \"email\",\n                                                                    autoComplete: \"email\",\n                                                                    required: true,\n                                                                    value: email,\n                                                                    onChange: (e)=>setEmail(e.target.value),\n                                                                    className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                    placeholder: \"Enter your email\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"password\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDD12 Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"password\",\n                                                                            name: \"password\",\n                                                                            type: showPassword ? 'text' : 'password',\n                                                                            autoComplete: \"current-password\",\n                                                                            required: true,\n                                                                            value: password,\n                                                                            onChange: (e)=>setPassword(e.target.value),\n                                                                            className: \"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Enter your password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 209,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors\",\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 27\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                                lineNumber: 228,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"remember-me\",\n                                                                            name: \"remember-me\",\n                                                                            type: \"checkbox\",\n                                                                            className: \"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"remember-me\",\n                                                                            className: \"ml-3 block text-sm font-medium text-gray-700\",\n                                                                            children: \"Remember me\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/auth/reset-password\",\n                                                                    className: \"text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors\",\n                                                                    children: \"Forgot password?\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            className: \"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Signing in...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this) : 'Sign In'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full border-t border-gray-200\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative flex justify-center text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-4 bg-white text-gray-500 font-medium\",\n                                                                    children: \"Or continue with\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleGoogleSignIn,\n                                                    disabled: isLoading,\n                                                    className: \"mt-6 w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 mr-3\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#4285F4\",\n                                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#34A853\",\n                                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#FBBC05\",\n                                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#EA4335\",\n                                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Continue with Google\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signup\",\n                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors\",\n                                                children: \"Sign up for free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(SignInPage, \"+xRkArW6MScNAdYNto9LixfmLrI=\");\n_c = SignInPage;\nvar _c;\n$RefreshReg$(_c, \"SignInPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signin/page.tsx\n"));

/***/ })

});