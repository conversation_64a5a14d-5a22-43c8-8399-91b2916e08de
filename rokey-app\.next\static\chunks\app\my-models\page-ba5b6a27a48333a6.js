(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5690],{38152:(e,r,t)=>{"use strict";t.d(r,{Pi:()=>n.A,fK:()=>a.A,uc:()=>o.A});var n=t(55628),o=t(31151),a=t(74500)},68492:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var n=t(6874),o=t.n(n),a=t(72227),c=t(94038),i=t(61316),d=t(85037),l=t(31151),s=t(80377),m=t(87162),O=t(74338),u=t(28003);function f(){let[e,r]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[t,n]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[f,N]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[h,D]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[E,_]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),U=(0,m.Z)(),[j,x]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),{createHoverPrefetch:b,prefetchManageKeysData:w}=(0,u._)(),v=async()=>{n(!0),N(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to fetch configurations")}let t=await e.json();r(t)}catch(e){N(e.message)}finally{n(!1)}};Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{v()},[]);let C=async e=>{if(e.preventDefault(),!h.trim())return void N("Configuration name cannot be empty.");_(!0),N(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:h})}),r=await e.json();if(!e.ok)throw Error(r.details||r.error||"Failed to create configuration");D(""),x(!1),await v()}catch(e){N(e.message)}finally{_(!1)}},g=(e,r)=>{U.showConfirmation({title:"Delete Configuration",message:'Are you sure you want to delete "'.concat(r,'"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.'),confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{N(null);try{let r=await fetch("/api/custom-configs/".concat(e),{method:"DELETE"}),t=await r.json();if(!r.ok)throw Error(t.details||t.error||"Failed to delete configuration");await v()}catch(e){throw N("Failed to delete: ".concat(e.message)),e}})};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-8 animate-fade-in",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:"text-4xl font-bold text-gray-900",children:"My API Models"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-600 mt-2",children:"Manage your custom API configurations and keys"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>x(!j),className:j?"btn-secondary":"btn-primary",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.A,{className:"h-4 w-4 mr-2"}),j?"Cancel":"Create New Model"]})]}),f&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"card border-red-200 bg-red-50 p-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-800",children:f})]})}),j&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"card max-w-md animate-scale-in p-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mb-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Create New Model"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-600",children:"Set up a new API configuration"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("form",{onSubmit:C,className:"space-y-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Name"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",id:"configName",value:h,onChange:e=>D(e.target.value),required:!0,className:"form-input",placeholder:"e.g., My Main Chat Assistant"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"submit",disabled:E,className:"btn-primary w-full",children:E?"Creating...":"Create Model"})]})]}),t&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,r)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(O.B0,{},r))}),!t&&!e.length&&!f&&!j&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"card text-center py-12",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"max-w-md mx-auto",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.A,{className:"h-8 w-8 text-orange-600"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No API Models Yet"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-600 mb-6",children:"Create your first API model configuration to get started with RoKey."}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>x(!0),className:"btn-primary",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})]})}),!t&&e.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,r)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(100*r,"ms")},children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-start justify-between mb-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex-1",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-xl font-semibold text-gray-900 mb-2 truncate",children:e.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-1",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center text-xs text-gray-600",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center text-xs text-gray-600",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(a.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.A,{className:"h-6 w-6 text-orange-600"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(o(),{href:"/my-models/".concat(e.id),className:"flex-1",...b(e.id),children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{className:"btn-primary w-full",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(i.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>g(e.id,e.name),className:"btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(l.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(s.A,{isOpen:U.isOpen,onClose:U.hideConfirmation,onConfirm:U.onConfirm,title:U.title,message:U.message,confirmText:U.confirmText,cancelText:U.cancelText,type:U.type,isLoading:U.isLoading})]})}!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},69528:(e,r,t)=>{Promise.resolve().then(t.bind(t,68492))}},e=>{var r=r=>e(e.s=r);e.O(0,[274,5738,1486,2662,8669,8848,4696,9173,6642,7706,7544,2138,8899,5495,7358],()=>r(69528)),_N_E=e.O()}]);