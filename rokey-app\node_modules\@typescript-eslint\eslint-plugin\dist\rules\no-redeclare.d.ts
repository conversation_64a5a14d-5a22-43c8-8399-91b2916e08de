import type { TSESLint } from '@typescript-eslint/utils';
export type MessageIds = 'redeclared' | 'redeclaredAsBuiltin' | 'redeclaredBySyntax';
export type Options = [
    {
        builtinGlobals?: boolean;
        ignoreDeclarationMerge?: boolean;
    }
];
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-redeclare.d.ts.map