"use strict";(()=>{var e={};e.id=6137,e.ids=[6137],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53810:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>h});var o=r(96559),n=r(48088),a=r(37719),i=r(32190),p=r(2507),u=r(62854),l=r(5649),c=r(55511),d=r.n(c);async function h(e,{params:t}){let{executionId:r}=t;if(!r)return i.NextResponse.json({error:"Execution ID is required"},{status:400});let s=(0,p.x)();try{let{data:e,error:t}=await s.from("orchestration_executions").select("*").eq("id",r).single();if(t||!e)return i.NextResponse.json({error:"Orchestration execution not found"},{status:404});let{data:o,error:n}=await s.from("orchestration_steps").select("step_number, role_id, response, prompt").eq("execution_id",r).eq("status","completed").order("step_number",{ascending:!0});if(n||!o||0===o.length)return i.NextResponse.json({error:"No completed steps found for synthesis"},{status:400});let a=o[0]?.prompt?.split('"')[1]||"user request",p=`You are the final moderator synthesizing the work of multiple AI specialists who collaborated on this request: "${a}"

Here are the outputs from each specialist:

${o.map(e=>`**${e.role_id.toUpperCase()} (Step ${e.step_number}):**
${e.response}

`).join("\n")}

Your task is to:
1. Combine these outputs into a single, cohesive, and complete response
2. Ensure the final result fully addresses the original user request
3. Present it in a clear, well-structured format
4. Include any necessary explanations or instructions for the user

Provide the final, polished response that the user will receive:`,c=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!c)return i.NextResponse.json({error:"Classification API key not found"},{status:500});let h=new l.y(c,r),m=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:p}],stream:!1,temperature:.3,max_tokens:8e3})});if(!m.ok){let e=await m.text().catch(()=>"Could not read error response");return i.NextResponse.json({error:`Synthesis API call failed: ${m.status}, ${e}`},{status:m.status})}let x=await m.json(),f=x.choices?.[0]?.message?.content||x.choices?.[0]?.text||"Synthesis completed but no content was returned.";await s.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString(),final_response:f}).eq("id",r);let y=e?.created_at?Date.now()-new Date(e.created_at).getTime():0;return(0,u.broadcastOrchestrationEvent)(r,{id:d().randomUUID(),execution_id:r,type:"orchestration_completed",timestamp:new Date().toISOString(),data:{commentary:h.generateLiveCommentary("orchestration_completed",{totalSteps:o.length}),finalResult:f,totalSteps:o.length,totalDuration:y}}),i.NextResponse.json({result:f})}catch(e){return i.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/orchestration/synthesis-fallback/[executionId]/route",pathname:"/api/orchestration/synthesis-fallback/[executionId]",filename:"route",bundlePath:"app/api/orchestration/synthesis-fallback/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\synthesis-fallback\\[executionId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:y}=m;function g(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410,7266],()=>r(53810));module.exports=s})();