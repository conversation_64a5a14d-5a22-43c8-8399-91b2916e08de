import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { executionId: string } }
) {
  const executionId = (await params).executionId;
  const supabase = createSupabaseServerClientOnRequest();

  try {
    // Get the execution record
    const { data: execution, error: executionError } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (executionError || !execution) {
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }

    // Get all steps for this execution
    const { data: steps, error: stepsError } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', executionId)
      .order('step_number', { ascending: true });

    if (stepsError) {
      return NextResponse.json(
        { error: 'Error fetching orchestration steps' },
        { status: 500 }
      );
    }

    // Format the response
    return NextResponse.json({
      execution: {
        id: execution.id,
        status: execution.status,
        created_at: execution.created_at,
        completed_at: execution.completed_at,
        total_steps: execution.total_steps,
        total_duration_ms: execution.total_duration_ms,
        total_tokens: execution.total_tokens,
        total_cost: execution.total_cost,
        original_prompt: execution.original_prompt
      },
      steps: steps.map(step => ({
        id: step.id,
        step_number: step.step_number,
        role_id: step.role_id,
        model_name: step.model_name,
        status: step.status,
        created_at: step.created_at,
        started_at: step.started_at,
        completed_at: step.completed_at,
        duration_ms: step.duration_ms,
        tokens_in: step.tokens_in,
        tokens_out: step.tokens_out,
        cost: step.cost,
        response: step.response,
        error_message: step.error_message
      }))
    });
  } catch (error) {
    console.error(`[Orchestration Status] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}