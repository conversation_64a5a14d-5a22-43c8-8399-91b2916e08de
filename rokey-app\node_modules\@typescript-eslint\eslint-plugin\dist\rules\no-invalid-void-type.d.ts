export interface Options {
    allowAsThisParameter?: boolean;
    allowInGenericTypeArguments?: boolean | [string, ...string[]];
}
export type MessageIds = 'invalidVoidForGeneric' | 'invalidVoidNotReturn' | 'invalidVoidNotReturnOrGeneric' | 'invalidVoidNotReturnOrThisParam' | 'invalidVoidNotReturnOrThisParamOrGeneric' | 'invalidVoidUnionConstituent';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, [Options], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-invalid-void-type.d.ts.map