"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/features/page",{

/***/ "(app-pages-browser)/./src/app/features/page.tsx":
/*!***********************************!*\
  !*** ./src/app/features/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Intelligent Routing\",\n        description: \"Advanced AI algorithms analyze your prompts and automatically route them to the optimal model for best results.\",\n        details: [\n            \"Real-time prompt analysis\",\n            \"Context-aware routing decisions\",\n            \"Performance optimization\",\n            \"Cost-effective model selection\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Bank-grade security with end-to-end encryption, compliance certifications, and audit trails.\",\n        details: [\n            \"SOC 2 Type II compliance\",\n            \"End-to-end encryption\",\n            \"Complete audit trails\",\n            \"Role-based access control\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Advanced Analytics\",\n        description: \"Comprehensive insights into model performance, costs, and usage patterns with real-time dashboards.\",\n        details: [\n            \"Real-time performance metrics\",\n            \"Cost optimization insights\",\n            \"Usage pattern analysis\",\n            \"Custom reporting\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access to the largest collection of AI models from leading providers, all through one unified API.\",\n        details: [\n            \"OpenAI, Anthropic, Google, Meta\",\n            \"Specialized domain models\",\n            \"Latest model versions\",\n            \"Custom model integration\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Auto-Failover\",\n        description: \"Automatic failover and retry mechanisms ensure 99.9% uptime for your AI applications.\",\n        details: [\n            \"Intelligent failover routing\",\n            \"Automatic retry logic\",\n            \"Health monitoring\",\n            \"Zero-downtime switching\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Global Infrastructure\",\n        description: \"Distributed infrastructure across multiple regions for low latency and high availability.\",\n        details: [\n            \"Multi-region deployment\",\n            \"Edge computing optimization\",\n            \"CDN acceleration\",\n            \"99.9% uptime SLA\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Developer-First\",\n        description: \"Built by developers, for developers. Simple APIs, comprehensive docs, and powerful SDKs.\",\n        details: [\n            \"RESTful API design\",\n            \"Multiple SDK languages\",\n            \"Interactive documentation\",\n            \"Code examples & tutorials\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Smart Optimization\",\n        description: \"Continuous learning algorithms optimize routing decisions based on your usage patterns.\",\n        details: [\n            \"Machine learning optimization\",\n            \"Pattern recognition\",\n            \"Adaptive routing\",\n            \"Performance tuning\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Rapid Deployment\",\n        description: \"Get started in minutes with our simple integration process and migration tools.\",\n        details: [\n            \"5-minute setup\",\n            \"Migration assistance\",\n            \"Zero-code integration\",\n            \"Instant activation\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"Custom Configurations\",\n        description: \"Flexible routing rules and custom configurations to match your specific requirements.\",\n        details: [\n            \"Custom routing logic\",\n            \"Business rule engine\",\n            \"A/B testing support\",\n            \"Configuration templates\"\n        ]\n    }\n];\nfunction FeaturesPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-slate-50 to-blue-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 15\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                        children: [\n                                            \"Powerful Features for\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#ff6b35] block\",\n                                                children: \"Modern AI Development\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                        children: \"Everything you need to build, deploy, and scale AI applications with confidence. From intelligent routing to enterprise security, we've got you covered.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 15\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3,\n                                            delay: index * 0.05\n                                        },\n                                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 bg-[#ff6b35] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        detail\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-6\",\n                                        children: \"Ready to Transform Your AI Development?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8\",\n                                        children: \"Join thousands of developers who trust RouKey for their AI infrastructure.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-[#ff6b35] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#e55a2b] transition-colors duration-200 shadow-lg\",\n                                        children: \"Get Started Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_c = FeaturesPage;\nvar _c;\n$RefreshReg$(_c, \"FeaturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZmVhdHVyZXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV1QztBQVlGO0FBQzBCO0FBQ2Q7QUFFakQsTUFBTWEsV0FBVztJQUNmO1FBQ0VDLE1BQU1iLDBOQUFRQTtRQUNkYyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsU0FBUztZQUNQO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUNBO1FBQ0VILE1BQU1aLDBOQUFlQTtRQUNyQmEsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFNBQVM7WUFDUDtZQUNBO1lBQ0E7WUFDQTtTQUNEO0lBQ0g7SUFDQTtRQUNFSCxNQUFNWCwwTkFBWUE7UUFDbEJZLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxTQUFTO1lBQ1A7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0lBQ0E7UUFDRUgsTUFBTVYsME5BQVdBO1FBQ2pCVyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsU0FBUztZQUNQO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUNBO1FBQ0VILE1BQU1ULDBOQUFhQTtRQUNuQlUsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFNBQVM7WUFDUDtZQUNBO1lBQ0E7WUFDQTtTQUNEO0lBQ0g7SUFDQTtRQUNFSCxNQUFNUiwwTkFBU0E7UUFDZlMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFNBQVM7WUFDUDtZQUNBO1lBQ0E7WUFDQTtTQUNEO0lBQ0g7SUFDQTtRQUNFSCxNQUFNUCwwTkFBZUE7UUFDckJRLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxTQUFTO1lBQ1A7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0lBQ0E7UUFDRUgsTUFBTU4sMk5BQWFBO1FBQ25CTyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsU0FBUztZQUNQO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUNBO1FBQ0VILE1BQU1MLDJOQUFnQkE7UUFDdEJNLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxTQUFTO1lBQ1A7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0lBQ0E7UUFDRUgsTUFBTUosMk5BQU9BO1FBQ2JLLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxTQUFTO1lBQ1A7WUFDQTtZQUNBO1lBQ0E7U0FDRDtJQUNIO0NBQ0Q7QUFFYyxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNULHlFQUFhQTs7Ozs7MEJBRWQsOERBQUNVO2dCQUFLRCxXQUFVOztrQ0FFZCw4REFBQ0U7d0JBQVFGLFdBQVU7a0NBQ2pCLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ3BCLGtEQUFNQSxDQUFDbUIsR0FBRztnQ0FDVEksU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRztnQ0FDN0JDLFNBQVM7b0NBQUVGLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUU7Z0NBQzVCRSxZQUFZO29DQUFFQyxVQUFVO2dDQUFJOztrREFFNUIsOERBQUNDO3dDQUFHVCxXQUFVOzs0Q0FBb0Q7MERBRWhFLDhEQUFDVTtnREFBS1YsV0FBVTswREFBdUI7Ozs7Ozs7Ozs7OztrREFFekMsOERBQUNXO3dDQUFFWCxXQUFVO2tEQUErQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTbEUsOERBQUNFO3dCQUFRRixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOzBDQUNaUCxTQUFTbUIsR0FBRyxDQUFDLENBQUNDLFNBQVNDLHNCQUN0Qiw4REFBQ2xDLGtEQUFNQSxDQUFDbUIsR0FBRzt3Q0FFVEksU0FBUzs0Q0FBRUMsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRzt3Q0FDN0JDLFNBQVM7NENBQUVGLFNBQVM7NENBQUdDLEdBQUc7d0NBQUU7d0NBQzVCRSxZQUFZOzRDQUFFQyxVQUFVOzRDQUFLTyxPQUFPRCxRQUFRO3dDQUFLO3dDQUNqRGQsV0FBVTtrREFFViw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNhLFFBQVFuQixJQUFJOzREQUFDTSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUc1Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDZ0I7NERBQUdoQixXQUFVO3NFQUNYYSxRQUFRbEIsS0FBSzs7Ozs7O3NFQUVoQiw4REFBQ2dCOzREQUFFWCxXQUFVO3NFQUNWYSxRQUFRakIsV0FBVzs7Ozs7O3NFQUV0Qiw4REFBQ3FCOzREQUFHakIsV0FBVTtzRUFDWGEsUUFBUWhCLE9BQU8sQ0FBQ2UsR0FBRyxDQUFDLENBQUNNLFFBQVFDLG9CQUM1Qiw4REFBQ0M7b0VBQWFwQixXQUFVOztzRkFDdEIsOERBQUNEOzRFQUFJQyxXQUFVOzs7Ozs7d0VBQ2RrQjs7bUVBRk1DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQXJCWk4sUUFBUWxCLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQW9DNUIsOERBQUNPO3dCQUFRRixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNwQixrREFBTUEsQ0FBQ21CLEdBQUc7Z0NBQ1RJLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUc7Z0NBQzdCQyxTQUFTO29DQUFFRixTQUFTO29DQUFHQyxHQUFHO2dDQUFFO2dDQUM1QkUsWUFBWTtvQ0FBRUMsVUFBVTtnQ0FBSTs7a0RBRTVCLDhEQUFDYTt3Q0FBR3JCLFdBQVU7a0RBQXFDOzs7Ozs7a0RBR25ELDhEQUFDVzt3Q0FBRVgsV0FBVTtrREFBNkI7Ozs7OztrREFHMUMsOERBQUNwQixrREFBTUEsQ0FBQzBDLE1BQU07d0NBQ1pDLFlBQVk7NENBQUVDLE9BQU87d0NBQUs7d0NBQzFCQyxVQUFVOzRDQUFFRCxPQUFPO3dDQUFLO3dDQUN4QnhCLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVQsOERBQUNSLGtFQUFNQTs7Ozs7Ozs7Ozs7QUFHYjtLQWhHd0JNIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxmZWF0dXJlc1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7XG4gIEJvbHRJY29uLFxuICBTaGllbGRDaGVja0ljb24sXG4gIENoYXJ0QmFySWNvbixcbiAgQ3B1Q2hpcEljb24sXG4gIEFycm93UGF0aEljb24sXG4gIENsb3VkSWNvbixcbiAgQ29kZUJyYWNrZXRJY29uLFxuICBMaWdodEJ1bGJJY29uLFxuICBSb2NrZXRMYXVuY2hJY29uLFxuICBDb2dJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgTGFuZGluZ05hdmJhciBmcm9tICdAL2NvbXBvbmVudHMvbGFuZGluZy9MYW5kaW5nTmF2YmFyJztcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvRm9vdGVyJztcblxuY29uc3QgZmVhdHVyZXMgPSBbXG4gIHtcbiAgICBpY29uOiBCb2x0SWNvbixcbiAgICB0aXRsZTogXCJJbnRlbGxpZ2VudCBSb3V0aW5nXCIsXG4gICAgZGVzY3JpcHRpb246IFwiQWR2YW5jZWQgQUkgYWxnb3JpdGhtcyBhbmFseXplIHlvdXIgcHJvbXB0cyBhbmQgYXV0b21hdGljYWxseSByb3V0ZSB0aGVtIHRvIHRoZSBvcHRpbWFsIG1vZGVsIGZvciBiZXN0IHJlc3VsdHMuXCIsXG4gICAgZGV0YWlsczogW1xuICAgICAgXCJSZWFsLXRpbWUgcHJvbXB0IGFuYWx5c2lzXCIsXG4gICAgICBcIkNvbnRleHQtYXdhcmUgcm91dGluZyBkZWNpc2lvbnNcIixcbiAgICAgIFwiUGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9uXCIsXG4gICAgICBcIkNvc3QtZWZmZWN0aXZlIG1vZGVsIHNlbGVjdGlvblwiXG4gICAgXVxuICB9LFxuICB7XG4gICAgaWNvbjogU2hpZWxkQ2hlY2tJY29uLFxuICAgIHRpdGxlOiBcIkVudGVycHJpc2UgU2VjdXJpdHlcIixcbiAgICBkZXNjcmlwdGlvbjogXCJCYW5rLWdyYWRlIHNlY3VyaXR5IHdpdGggZW5kLXRvLWVuZCBlbmNyeXB0aW9uLCBjb21wbGlhbmNlIGNlcnRpZmljYXRpb25zLCBhbmQgYXVkaXQgdHJhaWxzLlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiU09DIDIgVHlwZSBJSSBjb21wbGlhbmNlXCIsXG4gICAgICBcIkVuZC10by1lbmQgZW5jcnlwdGlvblwiLFxuICAgICAgXCJDb21wbGV0ZSBhdWRpdCB0cmFpbHNcIixcbiAgICAgIFwiUm9sZS1iYXNlZCBhY2Nlc3MgY29udHJvbFwiXG4gICAgXVxuICB9LFxuICB7XG4gICAgaWNvbjogQ2hhcnRCYXJJY29uLFxuICAgIHRpdGxlOiBcIkFkdmFuY2VkIEFuYWx5dGljc1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkNvbXByZWhlbnNpdmUgaW5zaWdodHMgaW50byBtb2RlbCBwZXJmb3JtYW5jZSwgY29zdHMsIGFuZCB1c2FnZSBwYXR0ZXJucyB3aXRoIHJlYWwtdGltZSBkYXNoYm9hcmRzLlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiUmVhbC10aW1lIHBlcmZvcm1hbmNlIG1ldHJpY3NcIixcbiAgICAgIFwiQ29zdCBvcHRpbWl6YXRpb24gaW5zaWdodHNcIixcbiAgICAgIFwiVXNhZ2UgcGF0dGVybiBhbmFseXNpc1wiLFxuICAgICAgXCJDdXN0b20gcmVwb3J0aW5nXCJcbiAgICBdXG4gIH0sXG4gIHtcbiAgICBpY29uOiBDcHVDaGlwSWNvbixcbiAgICB0aXRsZTogXCIzMDArIEFJIE1vZGVsc1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkFjY2VzcyB0byB0aGUgbGFyZ2VzdCBjb2xsZWN0aW9uIG9mIEFJIG1vZGVscyBmcm9tIGxlYWRpbmcgcHJvdmlkZXJzLCBhbGwgdGhyb3VnaCBvbmUgdW5pZmllZCBBUEkuXCIsXG4gICAgZGV0YWlsczogW1xuICAgICAgXCJPcGVuQUksIEFudGhyb3BpYywgR29vZ2xlLCBNZXRhXCIsXG4gICAgICBcIlNwZWNpYWxpemVkIGRvbWFpbiBtb2RlbHNcIixcbiAgICAgIFwiTGF0ZXN0IG1vZGVsIHZlcnNpb25zXCIsXG4gICAgICBcIkN1c3RvbSBtb2RlbCBpbnRlZ3JhdGlvblwiXG4gICAgXVxuICB9LFxuICB7XG4gICAgaWNvbjogQXJyb3dQYXRoSWNvbixcbiAgICB0aXRsZTogXCJBdXRvLUZhaWxvdmVyXCIsXG4gICAgZGVzY3JpcHRpb246IFwiQXV0b21hdGljIGZhaWxvdmVyIGFuZCByZXRyeSBtZWNoYW5pc21zIGVuc3VyZSA5OS45JSB1cHRpbWUgZm9yIHlvdXIgQUkgYXBwbGljYXRpb25zLlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiSW50ZWxsaWdlbnQgZmFpbG92ZXIgcm91dGluZ1wiLFxuICAgICAgXCJBdXRvbWF0aWMgcmV0cnkgbG9naWNcIixcbiAgICAgIFwiSGVhbHRoIG1vbml0b3JpbmdcIixcbiAgICAgIFwiWmVyby1kb3dudGltZSBzd2l0Y2hpbmdcIlxuICAgIF1cbiAgfSxcbiAge1xuICAgIGljb246IENsb3VkSWNvbixcbiAgICB0aXRsZTogXCJHbG9iYWwgSW5mcmFzdHJ1Y3R1cmVcIixcbiAgICBkZXNjcmlwdGlvbjogXCJEaXN0cmlidXRlZCBpbmZyYXN0cnVjdHVyZSBhY3Jvc3MgbXVsdGlwbGUgcmVnaW9ucyBmb3IgbG93IGxhdGVuY3kgYW5kIGhpZ2ggYXZhaWxhYmlsaXR5LlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiTXVsdGktcmVnaW9uIGRlcGxveW1lbnRcIixcbiAgICAgIFwiRWRnZSBjb21wdXRpbmcgb3B0aW1pemF0aW9uXCIsXG4gICAgICBcIkNETiBhY2NlbGVyYXRpb25cIixcbiAgICAgIFwiOTkuOSUgdXB0aW1lIFNMQVwiXG4gICAgXVxuICB9LFxuICB7XG4gICAgaWNvbjogQ29kZUJyYWNrZXRJY29uLFxuICAgIHRpdGxlOiBcIkRldmVsb3Blci1GaXJzdFwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkJ1aWx0IGJ5IGRldmVsb3BlcnMsIGZvciBkZXZlbG9wZXJzLiBTaW1wbGUgQVBJcywgY29tcHJlaGVuc2l2ZSBkb2NzLCBhbmQgcG93ZXJmdWwgU0RLcy5cIixcbiAgICBkZXRhaWxzOiBbXG4gICAgICBcIlJFU1RmdWwgQVBJIGRlc2lnblwiLFxuICAgICAgXCJNdWx0aXBsZSBTREsgbGFuZ3VhZ2VzXCIsXG4gICAgICBcIkludGVyYWN0aXZlIGRvY3VtZW50YXRpb25cIixcbiAgICAgIFwiQ29kZSBleGFtcGxlcyAmIHR1dG9yaWFsc1wiXG4gICAgXVxuICB9LFxuICB7XG4gICAgaWNvbjogTGlnaHRCdWxiSWNvbixcbiAgICB0aXRsZTogXCJTbWFydCBPcHRpbWl6YXRpb25cIixcbiAgICBkZXNjcmlwdGlvbjogXCJDb250aW51b3VzIGxlYXJuaW5nIGFsZ29yaXRobXMgb3B0aW1pemUgcm91dGluZyBkZWNpc2lvbnMgYmFzZWQgb24geW91ciB1c2FnZSBwYXR0ZXJucy5cIixcbiAgICBkZXRhaWxzOiBbXG4gICAgICBcIk1hY2hpbmUgbGVhcm5pbmcgb3B0aW1pemF0aW9uXCIsXG4gICAgICBcIlBhdHRlcm4gcmVjb2duaXRpb25cIixcbiAgICAgIFwiQWRhcHRpdmUgcm91dGluZ1wiLFxuICAgICAgXCJQZXJmb3JtYW5jZSB0dW5pbmdcIlxuICAgIF1cbiAgfSxcbiAge1xuICAgIGljb246IFJvY2tldExhdW5jaEljb24sXG4gICAgdGl0bGU6IFwiUmFwaWQgRGVwbG95bWVudFwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkdldCBzdGFydGVkIGluIG1pbnV0ZXMgd2l0aCBvdXIgc2ltcGxlIGludGVncmF0aW9uIHByb2Nlc3MgYW5kIG1pZ3JhdGlvbiB0b29scy5cIixcbiAgICBkZXRhaWxzOiBbXG4gICAgICBcIjUtbWludXRlIHNldHVwXCIsXG4gICAgICBcIk1pZ3JhdGlvbiBhc3Npc3RhbmNlXCIsXG4gICAgICBcIlplcm8tY29kZSBpbnRlZ3JhdGlvblwiLFxuICAgICAgXCJJbnN0YW50IGFjdGl2YXRpb25cIlxuICAgIF1cbiAgfSxcbiAge1xuICAgIGljb246IENvZ0ljb24sXG4gICAgdGl0bGU6IFwiQ3VzdG9tIENvbmZpZ3VyYXRpb25zXCIsXG4gICAgZGVzY3JpcHRpb246IFwiRmxleGlibGUgcm91dGluZyBydWxlcyBhbmQgY3VzdG9tIGNvbmZpZ3VyYXRpb25zIHRvIG1hdGNoIHlvdXIgc3BlY2lmaWMgcmVxdWlyZW1lbnRzLlwiLFxuICAgIGRldGFpbHM6IFtcbiAgICAgIFwiQ3VzdG9tIHJvdXRpbmcgbG9naWNcIixcbiAgICAgIFwiQnVzaW5lc3MgcnVsZSBlbmdpbmVcIixcbiAgICAgIFwiQS9CIHRlc3Rpbmcgc3VwcG9ydFwiLFxuICAgICAgXCJDb25maWd1cmF0aW9uIHRlbXBsYXRlc1wiXG4gICAgXVxuICB9XG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBGZWF0dXJlc1BhZ2UoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctd2hpdGVcIj5cbiAgICAgIDxMYW5kaW5nTmF2YmFyIC8+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cInB0LTIwXCI+XG4gICAgICAgIHsvKiBIZXJvIFNlY3Rpb24gKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdG8tYmx1ZS01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMTUgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC02eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPlxuICAgICAgICAgICAgICAgIFBvd2VyZnVsIEZlYXR1cmVzIGZvclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtWyNmZjZiMzVdIGJsb2NrXCI+TW9kZXJuIEFJIERldmVsb3BtZW50PC9zcGFuPlxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWF4LXctM3hsIG14LWF1dG8gbWItOFwiPlxuICAgICAgICAgICAgICAgIEV2ZXJ5dGhpbmcgeW91IG5lZWQgdG8gYnVpbGQsIGRlcGxveSwgYW5kIHNjYWxlIEFJIGFwcGxpY2F0aW9ucyB3aXRoIGNvbmZpZGVuY2UuXG4gICAgICAgICAgICAgICAgRnJvbSBpbnRlbGxpZ2VudCByb3V0aW5nIHRvIGVudGVycHJpc2Ugc2VjdXJpdHksIHdlJ3ZlIGdvdCB5b3UgY292ZXJlZC5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgey8qIEZlYXR1cmVzIEdyaWQgKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC0xMlwiPlxuICAgICAgICAgICAgICB7ZmVhdHVyZXMubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICBrZXk9e2ZlYXR1cmUudGl0bGV9XG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDE1IH19XG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMywgZGVsYXk6IGluZGV4ICogMC4wNSB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgcC04IHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWdyYXktMTAwIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctWyNmZjZiMzVdIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxmZWF0dXJlLmljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmUuZGV0YWlscy5tYXAoKGRldGFpbCwgaWR4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2lkeH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEuNSBoLTEuNSBiZy1bI2ZmNmIzNV0gcm91bmRlZC1mdWxsIG1yLTNcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGV0YWlsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICB7LyogQ1RBIFNlY3Rpb24gKi99XG4gICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYXktOTAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+XG4gICAgICAgICAgICAgICAgUmVhZHkgdG8gVHJhbnNmb3JtIFlvdXIgQUkgRGV2ZWxvcG1lbnQ/XG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTMwMCBtYi04XCI+XG4gICAgICAgICAgICAgICAgSm9pbiB0aG91c2FuZHMgb2YgZGV2ZWxvcGVycyB3aG8gdHJ1c3QgUm91S2V5IGZvciB0aGVpciBBSSBpbmZyYXN0cnVjdHVyZS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDUgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLVsjZmY2YjM1XSB0ZXh0LXdoaXRlIHB4LTggcHktNCByb3VuZGVkLXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1sZyBob3ZlcjpiZy1bI2U1NWEyYl0gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIHNoYWRvdy1sZ1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBHZXQgU3RhcnRlZCBOb3dcbiAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L3NlY3Rpb24+XG4gICAgICA8L21haW4+XG5cbiAgICAgIDxGb290ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJCb2x0SWNvbiIsIlNoaWVsZENoZWNrSWNvbiIsIkNoYXJ0QmFySWNvbiIsIkNwdUNoaXBJY29uIiwiQXJyb3dQYXRoSWNvbiIsIkNsb3VkSWNvbiIsIkNvZGVCcmFja2V0SWNvbiIsIkxpZ2h0QnVsYkljb24iLCJSb2NrZXRMYXVuY2hJY29uIiwiQ29nSWNvbiIsIkxhbmRpbmdOYXZiYXIiLCJGb290ZXIiLCJmZWF0dXJlcyIsImljb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZGV0YWlscyIsIkZlYXR1cmVzUGFnZSIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iLCJzZWN0aW9uIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImgxIiwic3BhbiIsInAiLCJtYXAiLCJmZWF0dXJlIiwiaW5kZXgiLCJkZWxheSIsImgzIiwidWwiLCJkZXRhaWwiLCJpZHgiLCJsaSIsImgyIiwiYnV0dG9uIiwid2hpbGVIb3ZlciIsInNjYWxlIiwid2hpbGVUYXAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/features/page.tsx\n"));

/***/ })

});