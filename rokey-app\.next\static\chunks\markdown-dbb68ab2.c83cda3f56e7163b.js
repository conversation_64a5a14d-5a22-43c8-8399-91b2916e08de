"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5006],{28831:(e,t,c)=>{c.d(t,{Ay:()=>g});var n=c(34093),o=c(94295),a=c(70275),r=c(9760),l=c(91498),i=c(49518),s=c(86212),u=c(81142);!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let d=[],p={allowDangerousHtml:!0},m=/^(https?|ircs?|mailto|xmpp)$/i,f=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function g(e){let t=function(e){let t=e.rehypePlugins||d,c=e.remarkPlugins||d,n=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...p}:p;return(0,i.l)().use(r.A).use(c).use(l.Ay,n).use(t)}(e),c=function(e){let t=e.children||"",c=new u.T;return"string"==typeof t?c.value=t:(0,n.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),c}(e);return function(e,t){let c=t.allowedElements,r=t.allowElement,l=t.components,i=t.disallowedElements,u=t.skipHtml,d=t.unwrapDisallowed,p=t.urlTransform||h;for(let e of f)Object.hasOwn(t,e.from)&&(0,n.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return c&&i&&(0,n.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),(0,s.YR)(e,function(e,t,n){if("raw"===e.type&&n&&"number"==typeof t)return u?n.children.splice(t,1):n.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in a.$)if(Object.hasOwn(a.$,t)&&Object.hasOwn(e.properties,t)){let c=e.properties[t],n=a.$[t];(null===n||n.includes(e.tagName))&&(e.properties[t]=p(String(c||""),t,e))}}if("element"===e.type){let o=c?!c.includes(e.tagName):!!i&&i.includes(e.tagName);if(!o&&r&&"number"==typeof t&&(o=!r(e,t,n)),o&&n&&"number"==typeof t)return d&&e.children?n.children.splice(t,1,...e.children):n.children.splice(t,1),t}}),(0,o.H)(e,{Fragment:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),components:l,ignoreInvalidStyle:!0,jsx:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),jsxs:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),passKeys:!0,passNode:!0})}(t.runSync(t.parse(c),c),e)}function h(e){let t=e.indexOf(":"),c=e.indexOf("?"),n=e.indexOf("#"),o=e.indexOf("/");return -1===t||-1!==o&&t>o||-1!==c&&t>c||-1!==n&&t>n||m.test(e.slice(0,t))?e:""}},67552:(e,t,c)=>{c.d(t,{A:()=>n});let n=["abap","abnf","actionscript","ada","agda","al","antlr4","apacheconf","apex","apl","applescript","aql","arduino","arff","asciidoc","asm6502","asmatmel","aspnet","autohotkey","autoit","avisynth","avro-idl","bash","basic","batch","bbcode","bicep","birb","bison","bnf","brainfuck","brightscript","bro","bsl","c","cfscript","chaiscript","cil","clike","clojure","cmake","cobol","coffeescript","concurnas","coq","cpp","crystal","csharp","cshtml","csp","css-extras","css","csv","cypher","d","dart","dataweave","dax","dhall","diff","django","dns-zone-file","docker","dot","ebnf","editorconfig","eiffel","ejs","elixir","elm","erb","erlang","etlua","excel-formula","factor","false","firestore-security-rules","flow","fortran","fsharp","ftl","gap","gcode","gdscript","gedcom","gherkin","git","glsl","gml","gn","go-module","go","graphql","groovy","haml","handlebars","haskell","haxe","hcl","hlsl","hoon","hpkp","hsts","http","ichigojam","icon","icu-message-format","idris","iecst","ignore","inform7","ini","io","j","java","javadoc","javadoclike","javascript","javastacktrace","jexl","jolie","jq","js-extras","js-templates","jsdoc","json","json5","jsonp","jsstacktrace","jsx","julia","keepalived","keyman","kotlin","kumir","kusto","latex","latte","less","lilypond","liquid","lisp","livescript","llvm","log","lolcode","lua","magma","makefile","markdown","markup-templating","markup","matlab","maxscript","mel","mermaid","mizar","mongodb","monkey","moonscript","n1ql","n4js","nand2tetris-hdl","naniscript","nasm","neon","nevod","nginx","nim","nix","nsis","objectivec","ocaml","opencl","openqasm","oz","parigp","parser","pascal","pascaligo","pcaxis","peoplecode","perl","php-extras","php","phpdoc","plsql","powerquery","powershell","processing","prolog","promql","properties","protobuf","psl","pug","puppet","pure","purebasic","purescript","python","q","qml","qore","qsharp","r","racket","reason","regex","rego","renpy","rest","rip","roboconf","robotframework","ruby","rust","sas","sass","scala","scheme","scss","shell-session","smali","smalltalk","smarty","sml","solidity","solution-file","soy","sparql","splunk-spl","sqf","sql","squirrel","stan","stylus","swift","systemd","t4-cs","t4-templating","t4-vb","tap","tcl","textile","toml","tremor","tsx","tt2","turtle","twig","typescript","typoscript","unrealscript","uorazor","uri","v","vala","vbnet","velocity","verilog","vhdl","vim","visual-basic","warpscript","wasm","web-idl","wiki","wolfram","wren","xeora","xml-doc","xojo","xquery","yaml","yang","zig"]},85830:(e,t,c)=>{c.d(t,{A:()=>O});var n=c(52673),o=c(53771),a=c(11566),r=c(79630);function l(e,t){var c=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),c.push.apply(c,n)}return c}function i(e){for(var t=1;t<arguments.length;t++){var c=null!=arguments[t]?arguments[t]:{};t%2?l(Object(c),!0).forEach(function(t){(0,a.A)(e,t,c[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(c,t))})}return e}!function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var s={};!function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var u=["language","children","style","customStyle","codeTagProps","useInlineStyles","showLineNumbers","showInlineLineNumbers","startingLineNumber","lineNumberContainerStyle","lineNumberStyle","wrapLines","wrapLongLines","lineProps","renderer","PreTag","CodeTag","code","astGenerator"];function d(e,t){var c=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),c.push.apply(c,n)}return c}function p(e){for(var t=1;t<arguments.length;t++){var c=null!=arguments[t]?arguments[t]:{};t%2?d(Object(c),!0).forEach(function(t){(0,a.A)(e,t,c[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(c)):d(Object(c)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(c,t))})}return e}var m=/\n/g;function f(e){var t,c,n,o,a=e.codeString,r=e.codeStyle,l=e.containerStyle,i=e.numberStyle,s=e.startingLineNumber;return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("code",{style:Object.assign({},r,void 0===l?{float:"left",paddingRight:"10px"}:l)},(c=(t={lines:a.replace(/\n$/,"").split("\n"),style:void 0===i?{}:i,startingLineNumber:s}).lines,n=t.startingLineNumber,o=t.style,c.map(function(e,t){var c=t+n;return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())("span",{key:"line-".concat(t),className:"react-syntax-highlighter-line-number",style:"function"==typeof o?o(c):o},"".concat(c,"\n"))})))}function g(e,t){return{type:"element",tagName:"span",properties:{key:"line-number--".concat(e),className:["comment","linenumber","react-syntax-highlighter-line-number"],style:t},children:[{type:"text",value:e}]}}function h(e,t,c){var n={display:"inline-block",minWidth:"".concat(c.toString().length,".25em"),paddingRight:"1em",textAlign:"right",userSelect:"none"},o="function"==typeof e?e(t):e;return p(p({},n),o)}function v(e){var t=e.children,c=e.lineNumber,n=e.lineNumberStyle,a=e.largestLineNumber,r=e.showInlineLineNumbers,l=e.lineProps,i=void 0===l?{}:l,s=e.className,u=void 0===s?[]:s,d=e.showLineNumbers,m=e.wrapLongLines,f=e.wrapLines,v=void 0!==f&&f?p({},"function"==typeof i?i(c):i):{};if(v.className=v.className?[].concat((0,o.A)(v.className.trim().split(/\s+/)),(0,o.A)(u)):u,c&&r){var y=h(n,c,a);t.unshift(g(c,y))}return m&d&&(v.style=p({display:"flex"},v.style)),{type:"element",tagName:"span",properties:v,children:t}}function y(e){var t=e.rows,c=e.stylesheet,n=e.useInlineStyles;return t.map(function(e,t){return function e(t){var c=t.node,n=t.stylesheet,o=t.style,a=t.useInlineStyles,l=t.key,u=c.properties,d=c.type,p=c.tagName,m=c.value;if("text"===d)return m;if(p){var f,g,h=(f=0,function(t){return f+=1,t.map(function(t,c){return e({node:t,stylesheet:n,useInlineStyles:a,key:"code-segment-".concat(f,"-").concat(c)})})});if(a){var v=Object.keys(n).reduce(function(e,t){return t.split(".").forEach(function(t){e.includes(t)||e.push(t)}),e},[]),y=u.className&&u.className.includes("token")?["token"]:[],b=u.className&&y.concat(u.className.filter(function(e){return!v.includes(e)}));g=i(i({},u),{},{className:b.join(" ")||void 0,style:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=arguments.length>2?arguments[2]:void 0;return(function(e){if(0===e.length||1===e.length)return e;var t,c=e.join(".");return s[c]||(s[c]=0===(t=e.length)||1===t?e:2===t?[e[0],e[1],"".concat(e[0],".").concat(e[1]),"".concat(e[1],".").concat(e[0])]:3===t?[e[0],e[1],e[2],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0])]:t>=4?[e[0],e[1],e[2],e[3],"".concat(e[0],".").concat(e[1]),"".concat(e[0],".").concat(e[2]),"".concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[0]),"".concat(e[1],".").concat(e[2]),"".concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[1]),"".concat(e[2],".").concat(e[3]),"".concat(e[3],".").concat(e[0]),"".concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[0]),"".concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[0],".").concat(e[1],".").concat(e[2],".").concat(e[3]),"".concat(e[0],".").concat(e[1],".").concat(e[3],".").concat(e[2]),"".concat(e[0],".").concat(e[2],".").concat(e[1],".").concat(e[3]),"".concat(e[0],".").concat(e[2],".").concat(e[3],".").concat(e[1]),"".concat(e[0],".").concat(e[3],".").concat(e[1],".").concat(e[2]),"".concat(e[0],".").concat(e[3],".").concat(e[2],".").concat(e[1]),"".concat(e[1],".").concat(e[0],".").concat(e[2],".").concat(e[3]),"".concat(e[1],".").concat(e[0],".").concat(e[3],".").concat(e[2]),"".concat(e[1],".").concat(e[2],".").concat(e[0],".").concat(e[3]),"".concat(e[1],".").concat(e[2],".").concat(e[3],".").concat(e[0]),"".concat(e[1],".").concat(e[3],".").concat(e[0],".").concat(e[2]),"".concat(e[1],".").concat(e[3],".").concat(e[2],".").concat(e[0]),"".concat(e[2],".").concat(e[0],".").concat(e[1],".").concat(e[3]),"".concat(e[2],".").concat(e[0],".").concat(e[3],".").concat(e[1]),"".concat(e[2],".").concat(e[1],".").concat(e[0],".").concat(e[3]),"".concat(e[2],".").concat(e[1],".").concat(e[3],".").concat(e[0]),"".concat(e[2],".").concat(e[3],".").concat(e[0],".").concat(e[1]),"".concat(e[2],".").concat(e[3],".").concat(e[1],".").concat(e[0]),"".concat(e[3],".").concat(e[0],".").concat(e[1],".").concat(e[2]),"".concat(e[3],".").concat(e[0],".").concat(e[2],".").concat(e[1]),"".concat(e[3],".").concat(e[1],".").concat(e[0],".").concat(e[2]),"".concat(e[3],".").concat(e[1],".").concat(e[2],".").concat(e[0]),"".concat(e[3],".").concat(e[2],".").concat(e[0],".").concat(e[1]),"".concat(e[3],".").concat(e[2],".").concat(e[1],".").concat(e[0])]:void 0),s[c]})(e.filter(function(e){return"token"!==e})).reduce(function(e,t){return i(i({},e),c[t])},t)}(u.className,Object.assign({},u.style,void 0===o?{}:o),n)})}else g=i(i({},u),{},{className:u.className.join(" ")});var O=h(c.children);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(p,(0,r.A)({key:l},g),O)}}({node:e,stylesheet:c,useInlineStyles:n,key:"code-segement".concat(t)})})}function b(e){return e&&void 0!==e.highlightAuto}function O(e,t){return function(c){var a=c.language,r=c.children,l=c.style,i=void 0===l?t:l,s=c.customStyle,d=void 0===s?{}:s,O=c.codeTagProps,w=void 0===O?{className:a?"language-".concat(a):void 0,style:p(p({},i['code[class*="language-"]']),i['code[class*="language-'.concat(a,'"]')])}:O,N=c.useInlineStyles,j=void 0===N||N,x=c.showLineNumbers,k=void 0!==x&&x,E=c.showInlineLineNumbers,L=void 0===E||E,D=c.startingLineNumber,S=void 0===D?1:D,U=c.lineNumberContainerStyle,_=c.lineNumberStyle,P=void 0===_?{}:_,T=c.wrapLines,C=c.wrapLongLines,q=void 0!==C&&C,A=c.lineProps,F=c.renderer,I=c.PreTag,M=void 0===I?"pre":I,H=c.CodeTag,z=void 0===H?"code":H,R=c.code,$=void 0===R?(Array.isArray(r)?r[0]:r)||"":R,G=c.astGenerator,B=(0,n.A)(c,u);G=G||e;var V=k?Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(f,{containerStyle:U,codeStyle:w.style||{},numberStyle:P,startingLineNumber:S,codeString:$}):null,K=i.hljs||i['pre[class*="language-"]']||{backgroundColor:"#fff"},W=b(G)?"hljs":"prismjs",Y=j?Object.assign({},B,{style:Object.assign({},K,d)}):Object.assign({},B,{className:B.className?"".concat(W," ").concat(B.className):W,style:Object.assign({},d)});if(q?w.style=p({whiteSpace:"pre-wrap"},w.style):w.style=p({whiteSpace:"pre"},w.style),!G)return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(M,Y,V,Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(z,w,$));(void 0===T&&F||q)&&(T=!0),F=F||y;var J=[{type:"text",value:$}],Q=function(e){var t=e.astGenerator,c=e.language,n=e.code,o=e.defaultCodeValue;if(b(t)){var a=-1!==t.listLanguages().indexOf(c);return"text"===c?{value:o,language:"text"}:a?t.highlight(c,n):t.highlightAuto(n)}try{return c&&"text"!==c?{value:t.highlight(n,c)}:{value:o}}catch(e){return{value:o}}}({astGenerator:G,language:a,code:$,defaultCodeValue:J});null===Q.language&&(Q.value=J);var X=Q.value.length;1===X&&"text"===Q.value[0].type&&(X=Q.value[0].value.split("\n").length);var Z=X+S,ee=function(e,t,c,n,a,r,l,i,s){var u,d=function e(t){for(var c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=0;a<t.length;a++){var r=t[a];if("text"===r.type)n.push(v({children:[r],className:(0,o.A)(new Set(c))}));else if(r.children){var l=c.concat(r.properties.className);e(r.children,l).forEach(function(e){return n.push(e)})}}return n}(e.value),p=[],f=-1,y=0;function b(e,o){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t||r.length>0?function(e,o){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return v({children:e,lineNumber:o,lineNumberStyle:i,largestLineNumber:l,showInlineLineNumbers:a,lineProps:c,className:r,showLineNumbers:n,wrapLongLines:s,wrapLines:t})}(e,o,r):function(e,t){if(n&&t&&a){var c=h(i,t,l);e.unshift(g(t,c))}return e}(e,o)}for(;y<d.length;)!function(){var e=d[y],t=e.children[0].value;if(t.match(m)){var c=t.split("\n");c.forEach(function(t,o){var a=n&&p.length+r,l={type:"text",value:"".concat(t,"\n")};if(0===o){var i=b(d.slice(f+1,y).concat(v({children:[l],className:e.properties.className})),a);p.push(i)}else if(o===c.length-1){var s=d[y+1]&&d[y+1].children&&d[y+1].children[0],u={type:"text",value:"".concat(t)};if(s){var m=v({children:[u],className:e.properties.className});d.splice(y+1,0,m)}else{var g=b([u],a,e.properties.className);p.push(g)}}else{var h=b([l],a,e.properties.className);p.push(h)}}),f=y}y++}();if(f!==d.length-1){var O=d.slice(f+1,d.length);if(O&&O.length){var w=b(O,n&&p.length+r);p.push(w)}}return t?p:(u=[]).concat.apply(u,p)}(Q,T,void 0===A?{}:A,k,L,S,Z,P,q);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(M,Y,Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(z,w,!L&&V,F({rows:ee,stylesheet:i,useInlineStyles:j})))}}}}]);