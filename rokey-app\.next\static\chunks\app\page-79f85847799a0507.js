(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{1985:(e,t,r)=>{Promise.resolve().then(r.bind(r,33792))},11509:(e,t,r)=>{"use strict";r.d(t,{G:()=>l});var a=r(12115);let l=a.forwardRef(function(e,t){let{title:r,titleId:l,...s}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},s),r?a.createElement("title",{id:l},r):null,a.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))})},30130:(e,t,r)=>{"use strict";r.d(t,{fl:()=>a.A,DQ:()=>l.A,ud:()=>i,Zu:()=>n.A,BZ:()=>c.A});var a=r(89416),l=r(64274),s=r(12115);let i=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))});var n=r(8246),c=r(86474)},33792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(95155),l=r(12115),s=r(13744),i=r(56075),n=r(88001),c=r(21310),o=r(42490),d=r(49750),u=r(43893),m=r(78923),h=r(75961),f=r(21394),v=r(71118);function x(){let[e,t]=(0,l.useState)(!1);return((0,v.j)(),(0,l.useEffect)(()=>{(0,s.Lz)({onSuccess:()=>console.log("✅ Service Worker registered for caching"),onUpdate:()=>console.log("\uD83D\uDD04 New content available"),onError:e=>console.warn("⚠️ Service Worker registration failed:",e)}),t(!0)},[]),(0,l.useEffect)(()=>{{let e=setTimeout(()=>{let e=document.createElement("link");e.rel="prefetch",e.href="/pricing",document.head.appendChild(e);let t=document.createElement("link");t.rel="prefetch",t.href="/auth/signup",document.head.appendChild(t);let r=document.createElement("link");r.rel="prefetch",r.href="/features",document.head.appendChild(r)},1e3);return()=>clearTimeout(e)}},[]),e)?(0,a.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden",children:[(0,a.jsx)(f.A,{gridSize:60,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,a.jsx)("div",{className:"absolute inset-0 z-0",children:(0,a.jsx)("div",{className:"h-screen relative",children:(0,a.jsx)(f.A,{gridSize:45,opacity:.048,color:"#ff6b35",variant:"tech",animated:!0,glowEffect:!0,className:"absolute inset-0"})})}),(0,a.jsx)("div",{className:"absolute inset-0 z-0",style:{top:"200vh"},children:(0,a.jsx)("div",{className:"h-screen relative",children:(0,a.jsx)(f.A,{gridSize:35,opacity:.056,color:"#000000",variant:"premium",animated:!0,className:"absolute inset-0"})})}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)(i.A,{}),(0,a.jsxs)("main",{children:[(0,a.jsx)(n.A,{}),(0,a.jsx)(c.A,{}),(0,a.jsx)(d.A,{}),(0,a.jsx)(o.A,{}),(0,a.jsx)(u.A,{}),(0,a.jsx)(m.A,{})]}),(0,a.jsx)(h.A,{})]})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-black text-sm",children:"Loading RouKey..."})]})})}},42791:(e,t,r)=>{"use strict";r.d(t,{DQ:()=>a.A,r9:()=>l.A,O4:()=>s.A,Vy:()=>i.A,YE:()=>n.A,xm:()=>c.A,tZ:()=>d,Zu:()=>u.A});var a=r(64274),l=r(5500),s=r(82771),i=r(37186),n=r(58397),c=r(28960),o=r(12115);let d=o.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?o.createElement("title",{id:a},r):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"}))});var u=r(8246)},44383:(e,t,r)=>{"use strict";r.d(t,{f:()=>l.A,t:()=>a.A});var a=r(69598),l=r(74500)},45176:(e,t,r)=>{"use strict";r.d(t,{S:()=>l.A,f:()=>a.A});var a=r(89416),l=r(29337)},52529:(e,t,r)=>{"use strict";r.d(t,{DQ:()=>a.A,R2:()=>c.A,Sr:()=>s.A,YE:()=>n.A,Zu:()=>o.A,r$:()=>i.A,r9:()=>l.A});var a=r(64274),l=r(5500),s=r(29337),i=r(78030),n=r(58397),c=r(61316),o=r(8246)}},e=>{var t=t=>e(e.s=t);e.O(0,[7871,2115,274,5738,1486,2662,8669,8848,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(1985)),_N_E=e.O()}]);