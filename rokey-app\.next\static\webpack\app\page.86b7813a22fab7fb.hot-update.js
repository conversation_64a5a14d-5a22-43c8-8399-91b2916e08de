"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx":
/*!***********************************************************!*\
  !*** ./src/components/landing/EnhancedGridBackground.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EnhancedGridBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction EnhancedGridBackground(param) {\n    let { className = '', gridSize = 40, opacity = 0.1, color = '#000000', animated = false, glowEffect = false, variant = 'subtle' } = param;\n    const getVariantStyles = ()=>{\n        // Convert color to rgba format for proper opacity handling\n        const getRgbaColor = (baseColor, alpha)=>{\n            if (baseColor === '#000000') {\n                return \"rgba(0, 0, 0, \".concat(alpha, \")\");\n            } else if (baseColor === '#ffffff') {\n                return \"rgba(255, 255, 255, \".concat(alpha, \")\");\n            } else if (baseColor === '#ff6b35') {\n                return \"rgba(255, 107, 53, \".concat(alpha, \")\");\n            } else {\n                // Fallback for other colors\n                return \"\".concat(baseColor).concat(Math.round(alpha * 255).toString(16).padStart(2, '0'));\n            }\n        };\n        // Enhanced opacity for much more visible grids - reduced by 20%\n        const enhancedOpacity = opacity * 3.2 * 0.8; // Reduced by 20% for better subtlety\n        switch(variant){\n            case 'tech':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px),\\n            radial-gradient(circle at 50% 50%, \").concat(getRgbaColor(color, enhancedOpacity * 0.5), \" 2px, transparent 2px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 4, \"px \").concat(gridSize * 4, \"px\"),\n                    animation: animated ? 'tech-grid-move 30s linear infinite' : 'none',\n                    mask: \"\\n            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),\\n            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)\\n          \",\n                    maskComposite: 'intersect',\n                    WebkitMask: \"\\n            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),\\n            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)\\n          \",\n                    WebkitMaskComposite: 'source-in'\n                };\n            case 'premium':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, enhancedOpacity), \" 0.5px, transparent 0.5px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity), \" 0.5px, transparent 0.5px),\\n            linear-gradient(\").concat(getRgbaColor(color, enhancedOpacity * 0.7), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity * 0.7), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px\"),\n                    animation: animated ? 'premium-grid-float 40s ease-in-out infinite' : 'none',\n                    mask: \"\\n            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),\\n            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),\\n            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)\\n          \",\n                    maskComposite: 'intersect',\n                    WebkitMask: \"\\n            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),\\n            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),\\n            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)\\n          \",\n                    WebkitMaskComposite: 'source-in'\n                };\n            default:\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px\"),\n                    animation: animated ? 'subtle-grid-drift 25s linear infinite' : 'none',\n                    mask: \"\\n            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),\\n            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)\\n          \",\n                    maskComposite: 'intersect',\n                    WebkitMask: \"\\n            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),\\n            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)\\n          \",\n                    WebkitMaskComposite: 'source-in'\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...getVariantStyles(),\n                    zIndex: 1,\n                    filter: glowEffect ? 'drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))' : 'none'\n                },\n                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                    [\n                        \"cdf0235daf430a20\",\n                        [\n                            gridSize,\n                            gridSize,\n                            gridSize * 0.5,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.7,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1\n                        ]\n                    ]\n                ]) + \" \" + \"absolute inset-0 pointer-events-none \".concat(className)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\EnhancedGridBackground.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"cdf0235daf430a20\",\n                dynamic: [\n                    gridSize,\n                    gridSize,\n                    gridSize * 0.5,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.7,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1\n                ],\n                children: \"@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\".concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_c = EnhancedGridBackground;\nvar _c;\n$RefreshReg$(_c, \"EnhancedGridBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xhbmRpbmcvRW5oYW5jZWRHcmlkQmFja2dyb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBY2UsU0FBU0EsdUJBQXVCLEtBUWpCO1FBUmlCLEVBQzdDQyxZQUFZLEVBQUUsRUFDZEMsV0FBVyxFQUFFLEVBQ2JDLFVBQVUsR0FBRyxFQUNiQyxRQUFRLFNBQVMsRUFDakJDLFdBQVcsS0FBSyxFQUNoQkMsYUFBYSxLQUFLLEVBQ2xCQyxVQUFVLFFBQVEsRUFDVSxHQVJpQjtJQVU3QyxNQUFNQyxtQkFBbUI7UUFDdkIsMkRBQTJEO1FBQzNELE1BQU1DLGVBQWUsQ0FBQ0MsV0FBbUJDO1lBQ3ZDLElBQUlELGNBQWMsV0FBVztnQkFDM0IsT0FBTyxpQkFBdUIsT0FBTkMsT0FBTTtZQUNoQyxPQUFPLElBQUlELGNBQWMsV0FBVztnQkFDbEMsT0FBTyx1QkFBNkIsT0FBTkMsT0FBTTtZQUN0QyxPQUFPLElBQUlELGNBQWMsV0FBVztnQkFDbEMsT0FBTyxzQkFBNEIsT0FBTkMsT0FBTTtZQUNyQyxPQUFPO2dCQUNMLDRCQUE0QjtnQkFDNUIsT0FBTyxHQUFlQyxPQUFaRixXQUFrRSxPQUF0REUsS0FBS0MsS0FBSyxDQUFDRixRQUFRLEtBQUtHLFFBQVEsQ0FBQyxJQUFJQyxRQUFRLENBQUMsR0FBRztZQUN6RTtRQUNGO1FBRUEsZ0VBQWdFO1FBQ2hFLE1BQU1DLGtCQUFrQmIsVUFBVSxNQUFNLEtBQUsscUNBQXFDO1FBRWxGLE9BQVFJO1lBQ04sS0FBSztnQkFDSCxPQUFPO29CQUNMVSxpQkFBaUIsaUNBRVVSLE9BRFBBLGFBQWFMLE9BQU9ZLGtCQUFpQixnRUFFbEJQLE9BRFpBLGFBQWFMLE9BQU9ZLGtCQUFpQiw0RUFDa0IsT0FBM0NQLGFBQWFMLE9BQU9ZLGtCQUFrQixNQUFLO29CQUVsRkUsZ0JBQWdCLEdBQWlCaEIsT0FBZEEsVUFBUyxPQUFvQkEsT0FBZkEsVUFBUyxRQUFvQkEsT0FBZEEsVUFBUyxPQUFvQkEsT0FBZkEsVUFBUyxRQUF3QkEsT0FBbEJBLFdBQVcsR0FBRSxPQUFrQixPQUFiQSxXQUFXLEdBQUU7b0JBQzVHaUIsV0FBV2QsV0FBVyx1Q0FBdUM7b0JBQzdEZSxNQUFPO29CQUtQQyxlQUFlO29CQUNmQyxZQUFhO29CQUtiQyxxQkFBcUI7Z0JBQ3ZCO1lBRUYsS0FBSztnQkFDSCxPQUFPO29CQUNMTixpQkFBaUIsaUNBRVVSLE9BRFBBLGFBQWFMLE9BQU9ZLGtCQUFpQixvRUFFckNQLE9BRE9BLGFBQWFMLE9BQU9ZLGtCQUFpQiw2REFFckNQLE9BRFBBLGFBQWFMLE9BQU9ZLGtCQUFrQixNQUFLLGdFQUNPLE9BQTNDUCxhQUFhTCxPQUFPWSxrQkFBa0IsTUFBSztvQkFFdEVFLGdCQUFnQixHQUFpQmhCLE9BQWRBLFVBQVMsT0FBb0JBLE9BQWZBLFVBQVMsUUFBb0JBLE9BQWRBLFVBQVMsT0FBb0JBLE9BQWZBLFVBQVMsUUFBd0JBLE9BQWxCQSxXQUFXLEdBQUUsT0FBd0JBLE9BQW5CQSxXQUFXLEdBQUUsUUFBd0JBLE9BQWxCQSxXQUFXLEdBQUUsT0FBa0IsT0FBYkEsV0FBVyxHQUFFO29CQUNqSmlCLFdBQVdkLFdBQVcsZ0RBQWdEO29CQUN0RWUsTUFBTztvQkFLUEMsZUFBZTtvQkFDZkMsWUFBYTtvQkFLYkMscUJBQXFCO2dCQUN2QjtZQUVGO2dCQUNFLE9BQU87b0JBQ0xOLGlCQUFpQixpQ0FFVVIsT0FEUEEsYUFBYUwsT0FBT1ksa0JBQWlCLGdFQUNPLE9BQXJDUCxhQUFhTCxPQUFPWSxrQkFBaUI7b0JBRWhFRSxnQkFBZ0IsR0FBaUJoQixPQUFkQSxVQUFTLE9BQWMsT0FBVEEsVUFBUztvQkFDMUNpQixXQUFXZCxXQUFXLDBDQUEwQztvQkFDaEVlLE1BQU87b0JBS1BDLGVBQWU7b0JBQ2ZDLFlBQWE7b0JBS2JDLHFCQUFxQjtnQkFDdkI7UUFDSjtJQUNGO0lBRUEscUJBQ0U7OzBCQUNFLDhEQUFDQztnQkFFQ0MsT0FBTztvQkFDTCxHQUFHakIsa0JBQWtCO29CQUNyQmtCLFFBQVE7b0JBQ1JDLFFBQVFyQixhQUFhLGtEQUFrRDtnQkFDekU7Ozs7OzRCQU9nQ0o7NEJBQWVBOzRCQUtoQkEsV0FBVzs0QkFBVUEsV0FBVzs0QkFDaENBOzRCQUFlQSxXQUFXOzRCQUMxQkEsV0FBVzs0QkFBVUE7NEJBTXJCQSxXQUFXOzRCQUFVQSxXQUFXLENBQUM7NEJBQ2pDQSxXQUFXOzRCQUFVQSxXQUFXOzRCQUNoQ0EsV0FBVyxDQUFDOzRCQUFVQSxXQUFXOzs7MkJBM0JyRCx3Q0FBa0QsT0FBVkQ7Ozs7Ozs7OztvQkFZbkJDO29CQUFlQTtvQkFLaEJBLFdBQVc7b0JBQVVBLFdBQVc7b0JBQ2hDQTtvQkFBZUEsV0FBVztvQkFDMUJBLFdBQVc7b0JBQVVBO29CQU1yQkEsV0FBVztvQkFBVUEsV0FBVyxDQUFDO29CQUNqQ0EsV0FBVztvQkFBVUEsV0FBVztvQkFDaENBLFdBQVcsQ0FBQztvQkFBVUEsV0FBVzs7Z0tBZmpCQSxPQUFmQSxpQkFBQUEsT0FBZUEsc0NBQUFBLE9BQWZBLGlCQUFBQSxPQUFlQSw2SUFBQUEsT0FBZkEsaUJBQUFBLE9BQWVBLHNDQUFBQSxPQUFmQSxpQkFBQUEsT0FBZUEsdUlBQUFBLE9BQWZBLGlCQUFBQSxPQUFlQSxzQ0FBQUEsT0FBZkEsaUJBQUFBLE9BQWVBLHdNQUFBQSxPQUFmQSxpQkFBQUEsT0FBZUEsMkNBQUFBLE9BQWZBLGlCQUFBQSxPQUFlQSx5Q0FBQUEsT0FBZkEsaUJBQUFBLE9BQWVBLHNDQUFBQSxPQUFmQSxpQkFLREEsT0FMZ0JBLDBLQUtLQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVyw4Q0FBWEEsT0FBckJBLFdBQVcsWUFDWEEsT0FEcUJBLFdBQVcsMERBQ2pCQSxPQUFmQSxpQkFBQUEsT0FBZUEsV0FBVyw2Q0FBWEEsT0FBZkEsaUJBQ0FBLE9BRGVBLFdBQVcseURBQ0xBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxvREFBQUEsT0FBckJBLFdBQVcsWUFGWEEsT0FFcUJBLHNRQUZBQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVyw4Q0FBWEEsT0FBckJBLFdBQVcsWUFDWEEsT0FEcUJBLFdBQVcsdURBQ2pCQSxPQUFmQSxpQkFBQUEsT0FBZUEsV0FBVyw2Q0FBWEEsT0FBZkEsaUJBQ0FBLE9BRGVBLFdBQVcsc0RBQ0xBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxvREFBQUEsT0FBckJBLFdBQVcsWUFGWEEsT0FFcUJBLDZQQUZBQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVyw4Q0FBWEEsT0FBckJBLFdBQVcsWUFDWEEsT0FEcUJBLFdBQVcscURBQ2pCQSxPQUFmQSxpQkFBQUEsT0FBZUEsV0FBVyw2Q0FBWEEsT0FBZkEsaUJBQ0FBLE9BRGVBLFdBQVcsb0RBQ0xBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxvREFBQUEsT0FBckJBLFdBQVcsWUFGWEEsT0FFcUJBLG9WQUZBQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVyxtREFBWEEsT0FBckJBLFdBQVcsWUFBWEEsT0FBcUJBLFdBQVcsaURBQVhBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLDhDQUFYQSxPQUFyQkEsV0FBVyxZQUNYQSxPQURxQkEsV0FBVywwREFDakJBLE9BQWZBLGlCQUFBQSxPQUFlQSxXQUFXLGtEQUFYQSxPQUFmQSxpQkFBQUEsT0FBZUEsV0FBVyxnREFBWEEsT0FBZkEsaUJBQUFBLE9BQWVBLFdBQVcsNkNBQVhBLE9BQWZBLGlCQUNBQSxPQURlQSxXQUFXLHlEQUNMQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEseURBQUFBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSx1REFBQUEsT0FBckJBLFdBQVcsWUFBWEEsT0FBcUJBLG9EQUFBQSxPQUFyQkEsV0FBVyxZQU1YQSxPQU5xQkEsa1dBTUFBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLENBQUMsNENBQVpBLE9BQXJCQSxXQUFXLFlBQ1hBLE9BRHFCQSxXQUFXLENBQUMsd0RBQ1pBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLDJDQUFYQSxPQUFyQkEsV0FBVyxZQUNYQSxPQURxQkEsV0FBVyx1REFDVkEsT0FBdEJBLFdBQVcsQ0FBQyxZQUFaQSxPQUFzQkEsV0FBVyw2Q0FBWEEsT0FBdEJBLFdBQVcsQ0FBQyxZQUZaQSxPQUVzQkEsV0FBVyx5S0FGWkEsT0FBckJBLFdBQVcsWUFBWEEsT0FBcUJBLFdBQVcsQ0FBQyw0Q0FBWkEsT0FBckJBLFdBQVcsWUFDWEEsT0FEcUJBLFdBQVcsQ0FBQyxxREFDWkEsT0FBckJBLFdBQVcsWUFBWEEsT0FBcUJBLFdBQVcsMkNBQVhBLE9BQXJCQSxXQUFXLFlBQ1hBLE9BRHFCQSxXQUFXLG9EQUNWQSxPQUF0QkEsV0FBVyxDQUFDLFlBQVpBLE9BQXNCQSxXQUFXLDZDQUFYQSxPQUF0QkEsV0FBVyxDQUFDLFlBRlpBLE9BRXNCQSxXQUFXLG1LQUZaQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVyxDQUFDLDRDQUFaQSxPQUFyQkEsV0FBVyxZQUNYQSxPQURxQkEsV0FBVyxDQUFDLG1EQUNaQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVywyQ0FBWEEsT0FBckJBLFdBQVcsWUFDWEEsT0FEcUJBLFdBQVcsa0RBQ1ZBLE9BQXRCQSxXQUFXLENBQUMsWUFBWkEsT0FBc0JBLFdBQVcsNkNBQVhBLE9BQXRCQSxXQUFXLENBQUMsWUFGWkEsT0FFc0JBLFdBQVcsb1BBRlpBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLENBQUMsaURBQVpBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLENBQUMsK0NBQVpBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLENBQUMsNENBQVpBLE9BQXJCQSxXQUFXLFlBQ1hBLE9BRHFCQSxXQUFXLENBQUMsd0RBQ1pBLE9BQXJCQSxXQUFXLFlBQVhBLE9BQXFCQSxXQUFXLGdEQUFYQSxPQUFyQkEsV0FBVyxZQUFYQSxPQUFxQkEsV0FBVyw4Q0FBWEEsT0FBckJBLFdBQVcsWUFBWEEsT0FBcUJBLFdBQVcsMkNBQVhBLE9BQXJCQSxXQUFXLFlBQ1hBLE9BRHFCQSxXQUFXLHVEQUNWQSxPQUF0QkEsV0FBVyxDQUFDLFlBQVpBLE9BQXNCQSxXQUFXLGtEQUFYQSxPQUF0QkEsV0FBVyxDQUFDLFlBQVpBLE9BQXNCQSxXQUFXLGdEQUFYQSxPQUF0QkEsV0FBVyxDQUFDLFlBQVpBLE9BQXNCQSxXQUFXLDZDQUFYQSxPQUF0QkEsV0FBVyxDQUFDLG1CQUFVQSxXQUFXOzs7O0FBS3hFO0tBdkl3QkYiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxsYW5kaW5nXFxFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgRW5oYW5jZWRHcmlkQmFja2dyb3VuZFByb3BzIHtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBncmlkU2l6ZT86IG51bWJlcjtcbiAgb3BhY2l0eT86IG51bWJlcjtcbiAgY29sb3I/OiBzdHJpbmc7XG4gIGFuaW1hdGVkPzogYm9vbGVhbjtcbiAgZ2xvd0VmZmVjdD86IGJvb2xlYW47XG4gIHZhcmlhbnQ/OiAnc3VidGxlJyB8ICd0ZWNoJyB8ICdwcmVtaXVtJztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRW5oYW5jZWRHcmlkQmFja2dyb3VuZCh7IFxuICBjbGFzc05hbWUgPSAnJywgXG4gIGdyaWRTaXplID0gNDAsXG4gIG9wYWNpdHkgPSAwLjEsXG4gIGNvbG9yID0gJyMwMDAwMDAnLFxuICBhbmltYXRlZCA9IGZhbHNlLFxuICBnbG93RWZmZWN0ID0gZmFsc2UsXG4gIHZhcmlhbnQgPSAnc3VidGxlJ1xufTogRW5oYW5jZWRHcmlkQmFja2dyb3VuZFByb3BzKSB7XG4gIFxuICBjb25zdCBnZXRWYXJpYW50U3R5bGVzID0gKCkgPT4ge1xuICAgIC8vIENvbnZlcnQgY29sb3IgdG8gcmdiYSBmb3JtYXQgZm9yIHByb3BlciBvcGFjaXR5IGhhbmRsaW5nXG4gICAgY29uc3QgZ2V0UmdiYUNvbG9yID0gKGJhc2VDb2xvcjogc3RyaW5nLCBhbHBoYTogbnVtYmVyKSA9PiB7XG4gICAgICBpZiAoYmFzZUNvbG9yID09PSAnIzAwMDAwMCcpIHtcbiAgICAgICAgcmV0dXJuIGByZ2JhKDAsIDAsIDAsICR7YWxwaGF9KWA7XG4gICAgICB9IGVsc2UgaWYgKGJhc2VDb2xvciA9PT0gJyNmZmZmZmYnKSB7XG4gICAgICAgIHJldHVybiBgcmdiYSgyNTUsIDI1NSwgMjU1LCAke2FscGhhfSlgO1xuICAgICAgfSBlbHNlIGlmIChiYXNlQ29sb3IgPT09ICcjZmY2YjM1Jykge1xuICAgICAgICByZXR1cm4gYHJnYmEoMjU1LCAxMDcsIDUzLCAke2FscGhhfSlgO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gRmFsbGJhY2sgZm9yIG90aGVyIGNvbG9yc1xuICAgICAgICByZXR1cm4gYCR7YmFzZUNvbG9yfSR7TWF0aC5yb3VuZChhbHBoYSAqIDI1NSkudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDIsICcwJyl9YDtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gRW5oYW5jZWQgb3BhY2l0eSBmb3IgbXVjaCBtb3JlIHZpc2libGUgZ3JpZHMgLSByZWR1Y2VkIGJ5IDIwJVxuICAgIGNvbnN0IGVuaGFuY2VkT3BhY2l0eSA9IG9wYWNpdHkgKiAzLjIgKiAwLjg7IC8vIFJlZHVjZWQgYnkgMjAlIGZvciBiZXR0ZXIgc3VidGxldHlcblxuICAgIHN3aXRjaCAodmFyaWFudCkge1xuICAgICAgY2FzZSAndGVjaCc6XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgYmFja2dyb3VuZEltYWdlOiBgXG4gICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQoJHtnZXRSZ2JhQ29sb3IoY29sb3IsIGVuaGFuY2VkT3BhY2l0eSl9IDFweCwgdHJhbnNwYXJlbnQgMXB4KSxcbiAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCg5MGRlZywgJHtnZXRSZ2JhQ29sb3IoY29sb3IsIGVuaGFuY2VkT3BhY2l0eSl9IDFweCwgdHJhbnNwYXJlbnQgMXB4KSxcbiAgICAgICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNTAlIDUwJSwgJHtnZXRSZ2JhQ29sb3IoY29sb3IsIGVuaGFuY2VkT3BhY2l0eSAqIDAuNSl9IDJweCwgdHJhbnNwYXJlbnQgMnB4KVxuICAgICAgICAgIGAsXG4gICAgICAgICAgYmFja2dyb3VuZFNpemU6IGAke2dyaWRTaXplfXB4ICR7Z3JpZFNpemV9cHgsICR7Z3JpZFNpemV9cHggJHtncmlkU2l6ZX1weCwgJHtncmlkU2l6ZSAqIDR9cHggJHtncmlkU2l6ZSAqIDR9cHhgLFxuICAgICAgICAgIGFuaW1hdGlvbjogYW5pbWF0ZWQgPyAndGVjaC1ncmlkLW1vdmUgMzBzIGxpbmVhciBpbmZpbml0ZScgOiAnbm9uZScsXG4gICAgICAgICAgbWFzazogYFxuICAgICAgICAgICAgcmFkaWFsLWdyYWRpZW50KGVsbGlwc2UgOTAlIDkwJSBhdCBjZW50ZXIsIGJsYWNrIDEwJSwgdHJhbnNwYXJlbnQgODUlKSxcbiAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdHJhbnNwYXJlbnQgMCUsIGJsYWNrIDglLCBibGFjayA5MiUsIHRyYW5zcGFyZW50IDEwMCUpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgdHJhbnNwYXJlbnQgMCUsIGJsYWNrIDglLCBibGFjayA5MiUsIHRyYW5zcGFyZW50IDEwMCUpXG4gICAgICAgICAgYCxcbiAgICAgICAgICBtYXNrQ29tcG9zaXRlOiAnaW50ZXJzZWN0JyxcbiAgICAgICAgICBXZWJraXRNYXNrOiBgXG4gICAgICAgICAgICByYWRpYWwtZ3JhZGllbnQoZWxsaXBzZSA5MCUgOTAlIGF0IGNlbnRlciwgYmxhY2sgMTAlLCB0cmFuc3BhcmVudCA4NSUpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCB0cmFuc3BhcmVudCAwJSwgYmxhY2sgOCUsIGJsYWNrIDkyJSwgdHJhbnNwYXJlbnQgMTAwJSksXG4gICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCB0cmFuc3BhcmVudCAwJSwgYmxhY2sgOCUsIGJsYWNrIDkyJSwgdHJhbnNwYXJlbnQgMTAwJSlcbiAgICAgICAgICBgLFxuICAgICAgICAgIFdlYmtpdE1hc2tDb21wb3NpdGU6ICdzb3VyY2UtaW4nXG4gICAgICAgIH07XG5cbiAgICAgIGNhc2UgJ3ByZW1pdW0nOlxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KCR7Z2V0UmdiYUNvbG9yKGNvbG9yLCBlbmhhbmNlZE9wYWNpdHkpfSAwLjVweCwgdHJhbnNwYXJlbnQgMC41cHgpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KDkwZGVnLCAke2dldFJnYmFDb2xvcihjb2xvciwgZW5oYW5jZWRPcGFjaXR5KX0gMC41cHgsIHRyYW5zcGFyZW50IDAuNXB4KSxcbiAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCgke2dldFJnYmFDb2xvcihjb2xvciwgZW5oYW5jZWRPcGFjaXR5ICogMC43KX0gMXB4LCB0cmFuc3BhcmVudCAxcHgpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KDkwZGVnLCAke2dldFJnYmFDb2xvcihjb2xvciwgZW5oYW5jZWRPcGFjaXR5ICogMC43KX0gMXB4LCB0cmFuc3BhcmVudCAxcHgpXG4gICAgICAgICAgYCxcbiAgICAgICAgICBiYWNrZ3JvdW5kU2l6ZTogYCR7Z3JpZFNpemV9cHggJHtncmlkU2l6ZX1weCwgJHtncmlkU2l6ZX1weCAke2dyaWRTaXplfXB4LCAke2dyaWRTaXplICogNX1weCAke2dyaWRTaXplICogNX1weCwgJHtncmlkU2l6ZSAqIDV9cHggJHtncmlkU2l6ZSAqIDV9cHhgLFxuICAgICAgICAgIGFuaW1hdGlvbjogYW5pbWF0ZWQgPyAncHJlbWl1bS1ncmlkLWZsb2F0IDQwcyBlYXNlLWluLW91dCBpbmZpbml0ZScgOiAnbm9uZScsXG4gICAgICAgICAgbWFzazogYFxuICAgICAgICAgICAgcmFkaWFsLWdyYWRpZW50KGVsbGlwc2UgODUlIDg1JSBhdCBjZW50ZXIsIGJsYWNrIDE1JSwgdHJhbnNwYXJlbnQgODAlKSxcbiAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdHJhbnNwYXJlbnQgMiUsIGJsYWNrIDEyJSwgYmxhY2sgODglLCB0cmFuc3BhcmVudCA5OCUpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgdHJhbnNwYXJlbnQgMiUsIGJsYWNrIDEyJSwgYmxhY2sgODglLCB0cmFuc3BhcmVudCA5OCUpXG4gICAgICAgICAgYCxcbiAgICAgICAgICBtYXNrQ29tcG9zaXRlOiAnaW50ZXJzZWN0JyxcbiAgICAgICAgICBXZWJraXRNYXNrOiBgXG4gICAgICAgICAgICByYWRpYWwtZ3JhZGllbnQoZWxsaXBzZSA4NSUgODUlIGF0IGNlbnRlciwgYmxhY2sgMTUlLCB0cmFuc3BhcmVudCA4MCUpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCB0cmFuc3BhcmVudCAyJSwgYmxhY2sgMTIlLCBibGFjayA4OCUsIHRyYW5zcGFyZW50IDk4JSksXG4gICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCB0cmFuc3BhcmVudCAyJSwgYmxhY2sgMTIlLCBibGFjayA4OCUsIHRyYW5zcGFyZW50IDk4JSlcbiAgICAgICAgICBgLFxuICAgICAgICAgIFdlYmtpdE1hc2tDb21wb3NpdGU6ICdzb3VyY2UtaW4nXG4gICAgICAgIH07XG5cbiAgICAgIGRlZmF1bHQ6IC8vIHN1YnRsZVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KCR7Z2V0UmdiYUNvbG9yKGNvbG9yLCBlbmhhbmNlZE9wYWNpdHkpfSAxcHgsIHRyYW5zcGFyZW50IDFweCksXG4gICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICR7Z2V0UmdiYUNvbG9yKGNvbG9yLCBlbmhhbmNlZE9wYWNpdHkpfSAxcHgsIHRyYW5zcGFyZW50IDFweClcbiAgICAgICAgICBgLFxuICAgICAgICAgIGJhY2tncm91bmRTaXplOiBgJHtncmlkU2l6ZX1weCAke2dyaWRTaXplfXB4YCxcbiAgICAgICAgICBhbmltYXRpb246IGFuaW1hdGVkID8gJ3N1YnRsZS1ncmlkLWRyaWZ0IDI1cyBsaW5lYXIgaW5maW5pdGUnIDogJ25vbmUnLFxuICAgICAgICAgIG1hc2s6IGBcbiAgICAgICAgICAgIHJhZGlhbC1ncmFkaWVudChlbGxpcHNlIDk1JSA5NSUgYXQgY2VudGVyLCBibGFjayA1JSwgdHJhbnNwYXJlbnQgOTAlKSxcbiAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdHJhbnNwYXJlbnQgMCUsIGJsYWNrIDUlLCBibGFjayA5NSUsIHRyYW5zcGFyZW50IDEwMCUpLFxuICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgdHJhbnNwYXJlbnQgMCUsIGJsYWNrIDUlLCBibGFjayA5NSUsIHRyYW5zcGFyZW50IDEwMCUpXG4gICAgICAgICAgYCxcbiAgICAgICAgICBtYXNrQ29tcG9zaXRlOiAnaW50ZXJzZWN0JyxcbiAgICAgICAgICBXZWJraXRNYXNrOiBgXG4gICAgICAgICAgICByYWRpYWwtZ3JhZGllbnQoZWxsaXBzZSA5NSUgOTUlIGF0IGNlbnRlciwgYmxhY2sgNSUsIHRyYW5zcGFyZW50IDkwJSksXG4gICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsIHRyYW5zcGFyZW50IDAlLCBibGFjayA1JSwgYmxhY2sgOTUlLCB0cmFuc3BhcmVudCAxMDAlKSxcbiAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sIHRyYW5zcGFyZW50IDAlLCBibGFjayA1JSwgYmxhY2sgOTUlLCB0cmFuc3BhcmVudCAxMDAlKVxuICAgICAgICAgIGAsXG4gICAgICAgICAgV2Via2l0TWFza0NvbXBvc2l0ZTogJ3NvdXJjZS1pbidcbiAgICAgICAgfTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPGRpdlxuICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIHBvaW50ZXItZXZlbnRzLW5vbmUgJHtjbGFzc05hbWV9YH1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAuLi5nZXRWYXJpYW50U3R5bGVzKCksXG4gICAgICAgICAgekluZGV4OiAxLFxuICAgICAgICAgIGZpbHRlcjogZ2xvd0VmZmVjdCA/ICdkcm9wLXNoYWRvdygwIDAgMTBweCByZ2JhKDI1NSwgMTA3LCA1MywgMC4xKSknIDogJ25vbmUnLFxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICAgIFxuICAgICAgey8qIENTUyBBbmltYXRpb25zICovfVxuICAgICAgPHN0eWxlIGpzeD57YFxuICAgICAgICBAa2V5ZnJhbWVzIHN1YnRsZS1ncmlkLWRyaWZ0IHtcbiAgICAgICAgICAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlKDAsIDApOyB9XG4gICAgICAgICAgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlKCR7Z3JpZFNpemV9cHgsICR7Z3JpZFNpemV9cHgpOyB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIEBrZXlmcmFtZXMgdGVjaC1ncmlkLW1vdmUge1xuICAgICAgICAgIDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUoMCwgMCkgcm90YXRlKDBkZWcpOyB9XG4gICAgICAgICAgMjUlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUoJHtncmlkU2l6ZSAqIDAuNX1weCwgJHtncmlkU2l6ZSAqIDAuM31weCkgcm90YXRlKDAuNWRlZyk7IH1cbiAgICAgICAgICA1MCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgke2dyaWRTaXplfXB4LCAke2dyaWRTaXplICogMC43fXB4KSByb3RhdGUoMGRlZyk7IH1cbiAgICAgICAgICA3NSUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgke2dyaWRTaXplICogMC4zfXB4LCAke2dyaWRTaXplfXB4KSByb3RhdGUoLTAuNWRlZyk7IH1cbiAgICAgICAgICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUoMCwgMCkgcm90YXRlKDBkZWcpOyB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIEBrZXlmcmFtZXMgcHJlbWl1bS1ncmlkLWZsb2F0IHtcbiAgICAgICAgICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlKDAsIDApIHNjYWxlKDEpOyB9XG4gICAgICAgICAgMjUlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUoJHtncmlkU2l6ZSAqIDAuMn1weCwgJHtncmlkU2l6ZSAqIC0wLjF9cHgpIHNjYWxlKDEuMDEpOyB9XG4gICAgICAgICAgNTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGUoJHtncmlkU2l6ZSAqIDAuMX1weCwgJHtncmlkU2l6ZSAqIDAuMn1weCkgc2NhbGUoMC45OSk7IH1cbiAgICAgICAgICA3NSUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgke2dyaWRTaXplICogLTAuMX1weCwgJHtncmlkU2l6ZSAqIDAuMX1weCkgc2NhbGUoMS4wMDUpOyB9XG4gICAgICAgIH1cbiAgICAgIGB9PC9zdHlsZT5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kIiwiY2xhc3NOYW1lIiwiZ3JpZFNpemUiLCJvcGFjaXR5IiwiY29sb3IiLCJhbmltYXRlZCIsImdsb3dFZmZlY3QiLCJ2YXJpYW50IiwiZ2V0VmFyaWFudFN0eWxlcyIsImdldFJnYmFDb2xvciIsImJhc2VDb2xvciIsImFscGhhIiwiTWF0aCIsInJvdW5kIiwidG9TdHJpbmciLCJwYWRTdGFydCIsImVuaGFuY2VkT3BhY2l0eSIsImJhY2tncm91bmRJbWFnZSIsImJhY2tncm91bmRTaXplIiwiYW5pbWF0aW9uIiwibWFzayIsIm1hc2tDb21wb3NpdGUiLCJXZWJraXRNYXNrIiwiV2Via2l0TWFza0NvbXBvc2l0ZSIsImRpdiIsInN0eWxlIiwiekluZGV4IiwiZmlsdGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\n"));

/***/ })

});