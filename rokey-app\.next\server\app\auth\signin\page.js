(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43476:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(60687),s=a(43210),n=a(85814),o=a.n(n),l=a(30474),i=a(21121),d=a(94257),p=a(66524),c=a(56878);function m(){let[e,t]=(0,s.useState)(""),[a,n]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),[f,g]=(0,s.useState)(!1),[b,u]=(0,s.useState)(""),h=async e=>{e.preventDefault(),g(!0),u("");try{await new Promise(e=>setTimeout(e,1e3)),window.location.href="/dashboard"}catch(e){u("Invalid email or password. Please try again.")}finally{g(!1)}},$=async()=>{g(!0);try{await new Promise(e=>setTimeout(e,1e3)),window.location.href="/dashboard"}catch(e){u("Failed to sign in with Google. Please try again.")}finally{g(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)(c.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,r.jsx)("div",{className:"absolute inset-0",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,r.jsx)("div",{className:"relative z-10 w-full max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,r.jsx)(i.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:block",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:`
                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                  `,backgroundSize:"30px 30px"}}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm",children:(0,r.jsx)(l.default,{src:"/roukey_logo.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12 object-contain",priority:!0})}),(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Welcome to RouKey"}),(0,r.jsxs)("p",{className:"text-xl text-white/90 mb-8",children:["Access to ",(0,r.jsx)("span",{className:"font-bold",children:"UNLIMITED"})," AI requests across 300+ models"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{className:"text-white/90",children:"Intelligent Role Routing"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{className:"text-white/90",children:"Enterprise Security"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{className:"text-white/90",children:"No Request Limits"})]})]})]})]})}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"w-full max-w-md mx-auto lg:mx-0",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(o(),{href:"/",className:"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,r.jsx)(l.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,r.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]}),(0,r.jsx)("h2",{className:"text-4xl font-bold text-black mb-3",children:"Sign In"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg",children:"Welcome back to your AI gateway"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:`
                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                  `,backgroundSize:"20px 20px"}}),(0,r.jsxs)("div",{className:"relative z-10",children:[b&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,r.jsx)("p",{className:"text-red-600 text-sm",children:b})}),(0,r.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDCE7 Email Address"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your email"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"password",name:"password",type:m?"text":"password",autoComplete:"current-password",required:!0,value:a,onChange:e=>n(e.target.value),className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your password"}),(0,r.jsx)("button",{type:"button",onClick:()=>x(!m),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:m?(0,r.jsx)(d.A,{className:"h-5 w-5"}):(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg"}),(0,r.jsx)("label",{htmlFor:"remember-me",className:"ml-3 block text-sm font-medium text-gray-700",children:"Remember me"})]}),(0,r.jsx)(o(),{href:"/auth/reset-password",className:"text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Forgot password?"})]}),(0,r.jsx)("button",{type:"submit",disabled:f,className:"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:f?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-4 bg-white text-gray-500 font-medium",children:"Or continue with"})})]})}),(0,r.jsxs)("button",{onClick:$,disabled:f,className:"mt-6 w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md",children:[(0,r.jsxs)("svg",{className:"w-6 h-6 mr-3",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]})]})]}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)("p",{className:"text-gray-600 text-lg",children:["Don't have an account?"," ",(0,r.jsx)(o(),{href:"/auth/signup",className:"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors",children:"Sign up for free"})]})})]})]})})]})}},56878:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(60687),s=a(76180),n=a.n(s);function o({className:e="",gridSize:t=40,opacity:a=.1,color:s="#000000",animated:o=!1,glowEffect:l=!1,variant:i="subtle"}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{style:{...(()=>{let e=(e,t)=>"#000000"===e?`rgba(0, 0, 0, ${t})`:"#ffffff"===e?`rgba(255, 255, 255, ${t})`:"#ff6b35"===e?`rgba(255, 107, 53, ${t})`:`${e}${Math.round(255*t).toString(16).padStart(2,"0")}`,r=3.2*a*.8;switch(i){case"tech":return{backgroundImage:`
            linear-gradient(${e(s,r)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,r)} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${e(s,.5*r)} 2px, transparent 2px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${4*t}px ${4*t}px`,animation:o?"tech-grid-move 30s linear infinite":"none",mask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,WebkitMaskComposite:"source-in"};case"premium":return{backgroundImage:`
            linear-gradient(${e(s,r)} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${e(s,r)} 0.5px, transparent 0.5px),
            linear-gradient(${e(s,.7*r)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,.7*r)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${5*t}px ${5*t}px, ${5*t}px ${5*t}px`,animation:o?"premium-grid-float 40s ease-in-out infinite":"none",mask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,WebkitMaskComposite:"source-in"};default:return{backgroundImage:`
            linear-gradient(${e(s,r)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,r)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px`,animation:o?"subtle-grid-drift 25s linear infinite":"none",mask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}}})(),zIndex:1,filter:l?"drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))":"none"},className:n().dynamic([["cdf0235daf430a20",[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t]]])+" "+`absolute inset-0 pointer-events-none ${e}`}),(0,r.jsx)(n(),{id:"cdf0235daf430a20",dynamic:[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t],children:`@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);-moz-transform:translate(${t}px,${t}px);-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}`})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71963:(e,t,a)=>{Promise.resolve().then(a.bind(a,87578))},79551:e=>{"use strict";e.exports=require("url")},82579:(e,t,a)=>{Promise.resolve().then(a.bind(a,43476))},85443:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var r=a(65239),s=a(48088),n=a(88170),o=a.n(n),l=a(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let d={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,87578)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},87578:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,8153,1658,1121,5977,8947],()=>a(85443));module.exports=r})();