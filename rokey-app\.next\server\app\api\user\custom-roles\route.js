(()=>{var e={};e.id=1923,e.ids=[1923],e.modules={2507:(e,r,s)=>{"use strict";s.d(r,{x:()=>u});var t=s(34386),o=s(44999);async function u(){let e=await (0,o.UL)();return(0,t.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.get(r)?.value,set(r,s,t){try{e.set({name:r,value:s,...t})}catch(e){}},remove(r,s){try{e.set({name:r,value:"",...s})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13422:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{GET:()=>d,POST:()=>l});var o=s(96559),u=s(48088),a=s(37719),i=s(32190),n=s(2507),c=s(45697);let p=c.z.object({role_id:c.z.string().trim().min(1,"Role ID is required").max(30,"Role ID must be 30 characters or less").regex(/^[a-zA-Z0-9_]+$/,"Role ID can only contain letters, numbers, and underscores. No spaces or special characters."),name:c.z.string().trim().min(1,"Name is required").max(100,"Name must be 100 characters or less"),description:c.z.string().trim().max(500,"Description must be 500 characters or less").optional().nullable()});async function l(e){let r,s=(0,n.x)();try{r=await e.json()}catch(e){return i.NextResponse.json({error:"Invalid JSON request body."},{status:400})}let t=p.safeParse(r);if(!t.success)return i.NextResponse.json({error:"Invalid request body.",issues:t.error.flatten().fieldErrors},{status:400});let{role_id:o,name:u,description:a}=t.data;try{let{data:e,error:r}=await s.from("user_custom_roles").insert({user_id:"00000000-0000-0000-0000-000000000000",role_id:o,name:u,description:a}).select().single();if(r){if("23505"===r.code)return i.NextResponse.json({error:`You already have a custom role with ID '${o}'. Role IDs must be unique per user.`},{status:409});return i.NextResponse.json({error:"Failed to create custom role.",details:r.message},{status:500})}return i.NextResponse.json(e,{status:201})}catch(e){return i.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}async function d(e){let r=(0,n.x)();try{let{data:e,error:s}=await r.from("user_custom_roles").select("*").eq("user_id","00000000-0000-0000-0000-000000000000").order("name",{ascending:!0});if(s)return i.NextResponse.json({error:"Failed to fetch custom roles.",details:s.message},{status:500});return i.NextResponse.json(e||[],{status:200})}catch(e){return i.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/user/custom-roles/route",pathname:"/api/user/custom-roles",filename:"route",bundlePath:"app/api/user/custom-roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\custom-roles\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:y}=m;function v(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,9398,3410,5697],()=>s(13422));module.exports=t})();