exports.id=7266,exports.ids=[7266],exports.modules={2507:(e,t,o)=>{"use strict";o.d(t,{x:()=>n});var s=o(34386),i=o(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,o,s){try{e.set({name:t,value:o,...s})}catch(e){}},remove(t,o){try{e.set({name:t,value:"",...o})}catch(e){}}}})}},5649:(e,t,o)=>{"use strict";o.d(t,{y:()=>i});var s=o(68811);class i{constructor(e,t){this.classificationApiKey=e,this.executionId=t}async validateStepOutput(e,t,o,i,n){let a=`As an AI orchestration moderator, evaluate this step output:

Original Request: "${i}"
Role: ${t}
Expected Outcome: ${n}
Actual Output: "${o}"

Evaluate:
1. Does the output fulfill the role's responsibility?
2. Is the quality sufficient for the next step?
3. Are there any issues or gaps?
4. Can we proceed to the next step?

Respond in JSON format:
{
  "isValid": true/false,
  "quality": 0.85,
  "issues": ["list of any issues"],
  "suggestions": ["list of improvements"],
  "canProceed": true/false,
  "reasoning": "detailed explanation"
}`;try{let o=await this.callModerator(a),i=JSON.parse(o);return await (0,s.Zi)(this.executionId,"moderator_commentary",{commentary:`Validating ${t} output: ${i.reasoning}`,validation:i},e,t),i}catch(e){return{isValid:o.length>50,quality:.7,issues:[],suggestions:[],canProceed:!0}}}async resolveConflicts(e,t){let o=`As an AI orchestration moderator, resolve conflicts between multiple AI outputs:

Original Request: "${t}"

Conflicting Outputs:
${e.map((e,t)=>`${t+1}. ${e.roleId} (confidence: ${e.confidence}): "${e.output}"`).join("\n")}

Determine the best approach:
1. Choose the best output
2. Combine elements from multiple outputs
3. Request modifications
4. Escalate for human review

Respond in JSON format:
{
  "action": "proceed|retry|escalate|modify|synthesize",
  "reasoning": "detailed explanation",
  "modifications": "specific changes needed (if action is modify)",
  "confidence": 0.85,
  "nextSteps": ["list of next actions"]
}`;try{let e=await this.callModerator(o),t=JSON.parse(e);return await (0,s.Zi)(this.executionId,"moderator_commentary",{commentary:`Conflict resolution: ${t.reasoning}`,decision:t}),t}catch(o){let t=e.reduce((e,t)=>t.confidence>e.confidence?t:e);return{action:"proceed",reasoning:`Selected ${t.roleId} output with highest confidence (${t.confidence})`,confidence:.6,nextSteps:["continue_with_selected_output"]}}}async synthesizeOutputs(e,t){await (0,s.Zi)(this.executionId,"synthesis_started",{commentary:"\uD83E\uDDE9 Beginning synthesis of all specialist outputs...",totalSteps:e.length});let o=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${t}"

Specialist Outputs:
${e.map(e=>`${e.stepNumber}. ${e.roleId} (quality: ${e.quality}): "${e.output}"`).join("\n\n")}

Create a comprehensive, well-structured response that:
1. Integrates all valuable insights
2. Maintains logical flow
3. Resolves any contradictions
4. Provides a complete answer to the original request

Respond in JSON format:
{
  "combinedOutput": "the synthesized response",
  "methodology": "how you combined the outputs",
  "qualityScore": 0.92,
  "conflictsResolved": ["list of conflicts resolved"],
  "improvements": ["how the synthesis improved upon individual outputs"]
}`;try{await (0,s.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83D\uDD04 Analyzing specialist contributions...",progress:.3});let e=await this.callModerator(o);await (0,s.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83C\uDFA8 Weaving outputs together...",progress:.7});let t=JSON.parse(e);return await (0,s.Zi)(this.executionId,"synthesis_progress",{commentary:"✨ Finalizing synthesized response...",progress:1}),t}catch(t){return{combinedOutput:e.map(e=>`**${e.roleId} Contribution:**
${e.output}`).join("\n\n"),methodology:"Simple concatenation due to synthesis error",qualityScore:.6,conflictsResolved:[],improvements:[]}}}generateLiveCommentary(e,t){let o={step_started:[`🎬 ${t.roleId} is diving deep into this challenge...`,`⚡ Watch ${t.roleId} work their specialized magic!`,`🎯 ${t.roleId} is laser-focused on delivering excellence.`],step_progress:[`🔥 ${t.roleId} is making great progress...`,`⚙️ The gears are turning smoothly in ${t.roleId}'s domain!`,`🌟 ${t.roleId} is crafting something special...`],synthesis_started:["\uD83E\uDDE9 Time for the grand finale! I'm combining all these brilliant contributions...","\uD83C\uDFAD Watch as I orchestrate these individual masterpieces into one cohesive symphony!","\uD83C\uDF1F The magic happens now - weaving together all our specialists' expertise!"],orchestration_completed:["\uD83C\uDF89 What a performance! Our AI team delivered something truly remarkable.","✨ Mission accomplished! Each specialist played their part perfectly.","\uD83C\uDFC6 Outstanding collaboration - this is what AI teamwork looks like!"]}[e]||["\uD83E\uDD16 Processing..."];return o[Math.floor(Math.random()*o.length)]}async callModerator(e){let t=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.classificationApiKey}`},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert AI orchestration moderator. You coordinate multiple AI specialists, validate their work, resolve conflicts, and synthesize their outputs into cohesive results. Always respond in the requested JSON format."},{role:"user",content:e}],temperature:.2,max_tokens:2e3,response_format:{type:"json_object"}})});if(!t.ok)throw Error(`Moderator API error: ${t.status}`);let o=await t.json(),s=o.choices?.[0]?.message?.content;if(!s)throw Error("Empty moderator response");return s}async analyzeParallelizationOpportunities(e){let t=`Analyze these orchestration steps for parallelization opportunities:

Steps:
${e.map(e=>`${e.stepNumber}. ${e.roleId}: "${e.prompt}" (depends on: ${e.dependencies.join(", ")||"none"})`).join("\n")}

Determine:
1. Which steps can run in parallel
2. Optimal grouping strategy
3. Expected performance improvement

Respond in JSON format:
{
  "parallelGroups": [[1], [2, 3], [4]],
  "reasoning": "explanation of grouping strategy",
  "estimatedSpeedup": 1.8
}`;try{let e=await this.callModerator(t);return JSON.parse(e)}catch(t){return{parallelGroups:e.map(e=>[e.stepNumber]),reasoning:"Sequential execution due to analysis error",estimatedSpeedup:1}}}}},39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},62854:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GET:()=>a,broadcastOrchestrationEvent:()=>c,cleanupOldStreams:()=>u,getActiveStreamCount:()=>p});var s=o(32190),i=o(2507);let n=new Map;async function a(e,{params:t}){let{executionId:o}=await t;if(!o)return s.NextResponse.json({error:"Execution ID is required"},{status:400});let a=(0,i.x)();try{let{data:e,error:t}=await a.from("orchestration_executions").select("*").eq("id",o).single();if(t||!e)return s.NextResponse.json({error:"Orchestration execution not found"},{status:404});let i=new TextEncoder,c=new ReadableStream({start(t){n.set(o,{controller:t,lastEventId:"",startTime:Date.now()});let s={id:crypto.randomUUID(),execution_id:o,type:"stream_connected",timestamp:new Date().toISOString(),data:{message:"\uD83C\uDFAC Connected to AI team orchestration stream",execution:{id:e.id,status:e.status,total_steps:e.total_steps,created_at:e.created_at}}},a=l(s);t.enqueue(i.encode(a)),"pending"!==e.status&&r(o,t,i)},cancel(){n.delete(o)}});return new Response(c,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control"}})}catch(e){return s.NextResponse.json({error:"Internal server error"},{status:500})}}async function r(e,t,o){let s=(0,i.x)();try{let{data:i,error:n}=await s.from("orchestration_steps").select("*").eq("execution_id",e).order("step_number",{ascending:!0});if(n)return;for(let s of i||[])for(let i of function(e,t){let o=[],s={execution_id:t,step_number:e.step_number,role_id:e.role_id,model_name:e.model_name};return o.push({id:crypto.randomUUID(),...s,type:"step_assigned",timestamp:e.created_at,data:{commentary:`📋 ${e.role_id} specialist assigned to step ${e.step_number}`,step:{number:e.step_number,role:e.role_id,model:e.model_name,prompt:e.prompt.substring(0,100)+"..."}}}),e.started_at&&o.push({id:crypto.randomUUID(),...s,type:"step_started",timestamp:e.started_at,data:{commentary:`🚀 ${e.role_id} is now working on this challenge...`,estimatedDuration:e.duration_ms||45e3}}),"in_progress"===e.status&&o.push({id:crypto.randomUUID(),...s,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:`⚡ ${e.role_id} is making excellent progress...`,progress:.6,partialOutput:e.response?e.response.substring(0,200)+"...":null}}),"completed"===e.status&&e.completed_at&&o.push({id:crypto.randomUUID(),...s,type:"step_completed",timestamp:e.completed_at,data:{commentary:`✅ Outstanding work from ${e.role_id}! Moving to next phase.`,output:e.response,duration:e.duration_ms,tokens:{input:e.tokens_in,output:e.tokens_out},cost:e.cost,quality:.9}}),"failed"===e.status&&o.push({id:crypto.randomUUID(),...s,type:"step_failed",timestamp:e.completed_at||new Date().toISOString(),data:{commentary:`❌ ${e.role_id} encountered an issue. Analyzing options...`,error:e.error_message,retryPlan:"Attempting automatic recovery"}}),o}(s,e)){let e=l(i);t.enqueue(o.encode(e)),await new Promise(e=>setTimeout(e,100))}}catch(e){}}function l(e){return`id: ${e.id}
event: ${e.type}
data: ${JSON.stringify(e)}

`}function c(e,t){let o=n.get(e);if(o)try{let e=new TextEncoder,s=l(t);o.controller.enqueue(e.encode(s)),o.lastEventId=t.id}catch(t){n.delete(e)}}function p(){return n.size}function u(e=18e5){let t=Date.now();for(let[o,s]of n.entries())if(t-s.startTime>e){try{s.controller.close()}catch(e){}n.delete(o)}}},68811:(e,t,o)=>{"use strict";async function s(e,t,o,s,i,n){crypto.randomUUID(),new Date().toISOString()}function i(e,t,o){let s={orchestration_started:["\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.","\uD83D\uDE80 Alright everyone, we've got an exciting challenge ahead. Let me coordinate our specialists.","\uD83C\uDFAF Time to bring together our expert AI team for optimal results!"],task_decomposed:["\uD83D\uDCCB I've analyzed the task and assembled this expert team. Let's begin the collaboration!","\uD83C\uDFAA Perfect! I've matched the right specialists to each part of this challenge.","⚡ Task breakdown complete. Our team is perfectly positioned for success."],moderator_assignment:[`🎯 @${t}, you're up! Please begin your specialized work on this task.`,`📋 @${t}, this is right in your wheelhouse. Take it away!`,`⚡ @${t}, I need your expertise here. Please work your magic.`],specialist_acknowledgment:[`✅ Understood! I'm ${t} and I'll handle this task with expertise. Starting work now...`,`🎯 Perfect! As the ${t} specialist, I'm excited to tackle this challenge.`,`⚡ Got it! ${t} here - I'll deliver excellent results for the team.`],specialist_message:[`🎉 Excellent! I've completed my part of the task. Here's what I've delivered:`,`✨ Perfect! My specialized work is complete. Take a look at the results:`,`🚀 Mission accomplished! Here's my contribution to the team effort:`],handoff_message:[`✨ Excellent work, @${t}! Quality looks great. Now passing to the next specialist...`,`👏 Outstanding execution, @${t}! Moving to the next phase...`,`🎉 Fantastic results, @${t}! The team collaboration continues...`],synthesis_started:["\uD83E\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...","\uD83C\uDFA8 Amazing collaboration! Time to weave all these brilliant outputs together...","✨ Outstanding work team! Let me combine everything into the perfect final result..."],synthesis_complete:["\uD83C\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!","\uD83C\uDFC6 Incredible teamwork! We've created something truly exceptional together.","\uD83C\uDF1F Perfect execution! This is what happens when AI specialists collaborate brilliantly."]}[e]||["\uD83E\uDD16 Processing..."],i=s[Math.floor(Math.random()*s.length)];return o&&"string"==typeof i?i.replace(/\{(\w+)\}/g,(e,t)=>o[t]||e):i}o.d(t,{Zi:()=>s,re:()=>i})},78335:()=>{},96487:()=>{}};