(()=>{var e={};e.id=4881,e.ids=[4881],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75948:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{OPTIONS:()=>c,POST:()=>p});var o=r(96559),n=r(48088),a=r(37719),i=r(32190);let u=process.env.ROKEY_API_ACCESS_TOKEN;async function p(e){if(!u)return i.NextResponse.json({error:"Server configuration error: Master API token not configured."},{status:500});try{let t=await e.json(),r=!0===t.stream,s=new URL("/api/v1/chat/completions",e.url).toString(),o=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${u}`},body:JSON.stringify(t),cache:"no-store"});if(!o.ok){let e=await o.json().catch(()=>({error:{message:o.statusText}}));return i.NextResponse.json({error:`Error from backend completions API: ${e?.error?.message||o.statusText}`},{status:o.status})}if(r&&o.body){let e=new Headers(o.headers);return new Response(o.body,{status:o.status,statusText:o.statusText,headers:e})}{let e=await o.json();return i.NextResponse.json(e,{status:o.status})}}catch(t){let e="An unexpected error occurred in the playground proxy.";return t instanceof SyntaxError?e="Invalid JSON in request body.":t.message&&(e=t.message),i.NextResponse.json({error:e,details:t.toString()},{status:500})}}async function c(e){return i.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/playground/route",pathname:"/api/playground",filename:"route",bundlePath:"app/api/playground/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\playground\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=d;function y(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580],()=>r(75948));module.exports=s})();