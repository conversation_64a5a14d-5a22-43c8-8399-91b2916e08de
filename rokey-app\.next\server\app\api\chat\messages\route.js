(()=>{var e={};e.id=1719,e.ids=[1719],e.modules={289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{DELETE:()=>l,GET:()=>c,POST:()=>p,PUT:()=>d});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(2507);async function c(e){let t=(0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("conversation_id"),a=parseInt(r.get("limit")||"50"),n=parseInt(r.get("offset")||"0"),o="true"===r.get("latest");if(!s)return i.NextResponse.json({error:"conversation_id query parameter is required"},{status:400});if(a>100)return i.NextResponse.json({error:"Limit cannot exceed 100 messages"},{status:400});try{let e=t.from("chat_messages").select("*").eq("conversation_id",s);e=o?e.order("created_at",{ascending:!1}).limit(a):e.order("created_at",{ascending:!0}).range(n,n+a-1);let{data:r,error:u}=await e;if(u)return i.NextResponse.json({error:"Failed to fetch messages",details:u.message},{status:500});let c=o&&r?r.reverse():r,p=i.NextResponse.json(c||[],{status:200});return p.headers.set("Cache-Control","private, max-age=60"),p}catch(e){return i.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e){let t=(0,u.x)();try{let{conversation_id:r,role:s,content:a,api_key_id:n,model_used:o,temperature_used:u,tokens_prompt:c,tokens_completion:p,cost:d}=await e.json();if(!r||!s||!a)return i.NextResponse.json({error:"Missing required fields: conversation_id, role, content"},{status:400});if(!["user","assistant","system","error"].includes(s))return i.NextResponse.json({error:"Invalid role. Must be one of: user, assistant, system, error"},{status:400});if(!Array.isArray(a)||0===a.length)return i.NextResponse.json({error:"Content must be a non-empty array"},{status:400});let{data:l,error:x}=await t.from("chat_messages").insert({conversation_id:r,role:s,content:a,api_key_id:n,model_used:o,temperature_used:u,tokens_prompt:c,tokens_completion:p,cost:d}).select().single();if(x)return i.NextResponse.json({error:"Failed to create message",details:x.message},{status:500});return await t.from("chat_conversations").update({updated_at:new Date().toISOString()}).eq("id",r),i.NextResponse.json(l,{status:201})}catch(e){if("SyntaxError"===e.name)return i.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return i.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function d(e){let t=(0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return i.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{content:r}=await e.json();if(!r)return i.NextResponse.json({error:"content field is required"},{status:400});if(!Array.isArray(r)||0===r.length)return i.NextResponse.json({error:"Content must be a non-empty array"},{status:400});let{data:a,error:n}=await t.from("chat_messages").update({content:r}).eq("id",s).select().single();if(n)return i.NextResponse.json({error:"Failed to update message",details:n.message},{status:500});return i.NextResponse.json(a,{status:200})}catch(e){if("SyntaxError"===e.name)return i.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return i.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function l(e){let t=(0,u.x)(),{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return i.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{error:e}=await t.from("chat_messages").delete().eq("id",s);if(e)return i.NextResponse.json({error:"Failed to delete message",details:e.message},{status:500});return i.NextResponse.json({message:"Message deleted successfully"},{status:200})}catch(e){return i.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/chat/messages/route",pathname:"/api/chat/messages",filename:"route",bundlePath:"app/api/chat/messages/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\chat\\messages\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=x;function y(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var s=r(34386),a=r(44999);async function n(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410],()=>r(289));module.exports=s})();