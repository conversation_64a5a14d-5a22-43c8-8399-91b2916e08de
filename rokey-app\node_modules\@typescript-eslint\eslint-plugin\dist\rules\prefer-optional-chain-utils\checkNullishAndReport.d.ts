import type { ParserServicesWithTypeInformation, TSESTree } from '@typescript-eslint/utils';
import type { ReportDescriptor, RuleContext } from '@typescript-eslint/utils/ts-eslint';
import type { PreferOptionalChainMessageIds, PreferOptionalChainOptions } from './PreferOptionalChainOptions';
export declare function checkNullishAndReport(context: RuleContext<PreferOptionalChainMessageIds, [
    PreferOptionalChainOptions
]>, parserServices: ParserServicesWithTypeInformation, { requireNullish }: PreferOptionalChainOptions, maybeNullishNodes: TSESTree.Expression[], descriptor: ReportDescriptor<PreferOptionalChainMessageIds>): void;
//# sourceMappingURL=checkNullishAndReport.d.ts.map