(()=>{var e={};e.id=9468,e.ids=[9468],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var s=r(34386),o=r(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>I,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{OPTIONS:()=>g,PUT:()=>l});var o=r(96559),i=r(48088),n=r(37719),u=r(32190),a=r(2507),c=r(45697);let p=c.z.enum(["none","intelligent_role","complexity_round_robin","auto_optimal","strict_fallback"]),d=c.z.object({routing_strategy:p,routing_strategy_params:c.z.record(c.z.any()).nullable().optional()});async function l(e,{params:t}){let r,s=(0,a.x)(),o=(await t).configId;if(!o||"string"!=typeof o)return u.NextResponse.json({error:"Invalid configuration ID."},{status:400});try{r=await e.json()}catch(e){return u.NextResponse.json({error:"Invalid JSON request body."},{status:400})}let i=d.safeParse(r);if(!i.success)return u.NextResponse.json({error:"Invalid request body.",issues:i.error.flatten().fieldErrors},{status:400});let{routing_strategy:n,routing_strategy_params:c}=i.data;try{let{error:e}=await s.from("custom_api_configs").update({routing_strategy:n,routing_strategy_params:c,updated_at:new Date().toISOString()}).eq("id",o);if(e)return u.NextResponse.json({error:"Failed to update routing settings.",details:e.message},{status:500});return u.NextResponse.json({message:"Routing settings updated successfully.",routing_strategy:n,routing_strategy_params:c},{status:200})}catch(e){return u.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}async function g(){return u.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"PUT, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let x=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/custom-configs/[configId]/routing/route",pathname:"/api/custom-configs/[configId]/routing",filename:"route",bundlePath:"app/api/custom-configs/[configId]/routing/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\custom-configs\\[configId]\\routing\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:m}=x;function I(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410,5697],()=>r(99820));module.exports=s})();