(()=>{var e={};e.id=7545,e.ids=[7545],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18536:(e,s,a)=>{Promise.resolve().then(a.bind(a,35291))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25333:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=a(65239),r=a(48088),i=a(88170),n=a.n(i),l=a(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let o={children:["",{children:["routing-setup",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,26697)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(a.bind(a,71238)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/routing-setup/[configId]/page",pathname:"/routing-setup/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},26697:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32216:(e,s,a)=>{Promise.resolve().then(a.bind(a,26697))},33873:e=>{"use strict";e.exports=require("path")},44725:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(43210);let r=t.forwardRef(function({title:e,titleId:s,...a},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},a),e?t.createElement("title",{id:s},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},52074:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>y});var t=a(60687),r=a(43210),i=a(16189),n=a(85814),l=a.n(n),d=a(51426),o=a(62392),c=a(74461);let m=r.forwardRef(function({title:e,titleId:s,...a},t){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},a),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))});var u=a(70149),x=a(55296),h=a(58089),g=a(44725),p=a(60925);function b(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function j(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}let f=[{id:"none",name:"Default Behavior",shortDescription:"Automatic load balancing",description:"RoKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:d.A},{id:"intelligent_role",name:"Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"RoKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:o.A},{id:"complexity_round_robin",name:"Complexity-Based Round-Robin",shortDescription:"Route by prompt complexity",description:"RoKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:c.A},{id:"strict_fallback",name:"Strict Fallback",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RoKey will try them in sequence until one succeeds.",icon:m}];function v({apiKey:e,index:s,onMoveUp:a,onMoveDown:r}){return(0,t.jsx)("li",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-semibold text-orange-600",children:s+1})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.label}),(0,t.jsxs)("p",{className:"text-xs text-gray-600",children:[e.provider," - ",e.predefined_model_id]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[a&&(0,t.jsx)("button",{onClick:a,className:"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200",title:"Move up",children:(0,t.jsx)(u.A,{className:"w-4 h-4"})}),r&&(0,t.jsx)("button",{onClick:r,className:"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200",title:"Move down",children:(0,t.jsx)(x.A,{className:"w-4 h-4"})})]})]})})}function y(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),a=(0,i.useSearchParams)(),n=e.configId,{getCachedData:u,isCached:x}=(0,p.c)(),y=()=>{let e=a.get("from");return"routing-setup"===e?"/routing-setup":`/my-models/${n}`},[N,w]=(0,r.useState)(null),[k,_]=(0,r.useState)(!0),[C,A]=(0,r.useState)(null),[S,P]=(0,r.useState)(null),[R,L]=(0,r.useState)(!1),[M,E]=(0,r.useState)("none"),[I,K]=(0,r.useState)({}),[B,D]=(0,r.useState)([]),[F,$]=(0,r.useState)(!1),[q,O]=(0,r.useState)([]),[W,H]=(0,r.useState)(null),[T,z]=(0,r.useState)({}),[G,Z]=(0,r.useState)([]),[U,V]=(0,r.useState)(!1),[J,X]=(0,r.useState)(!1),[Q,Y]=(0,r.useState)(null),[ee,es]=(0,r.useState)(null);(0,r.useCallback)(async()=>{if(!n){A("Configuration ID is missing."),_(!1);return}let e=u(n);if(e&&e.configDetails&&e.apiKeys){w(e.configDetails),D(e.apiKeys);let s=e.routingStrategy||"none";if(E(s),K(e.routingParams||{}),"strict_fallback"===s&&e.routingParams?.ordered_api_key_ids){let s=e.routingParams.ordered_api_key_ids;O([...s.map(s=>e.apiKeys.find(e=>e.id===s)).filter(Boolean),...e.apiKeys.filter(e=>!s.includes(e.id))])}else O([...e.apiKeys]);_(!1),$(!1);return}x(n)||L(!0),_(!0),$(!0),A(null),P(null);try{let e=await fetch(`/api/custom-configs/${n}`);if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch configuration")}let s=await e.json();w(s);let a=s.routing_strategy||"none";E(a);let t=s.routing_strategy_params||{};K(t);let r=await fetch(`/api/keys?custom_config_id=${n}`);if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to fetch API keys for this configuration")}let i=await r.json();if(D(i),"strict_fallback"===a&&t.ordered_api_key_ids){let e=t.ordered_api_key_ids,s=e.map(e=>i.find(s=>s.id===e)).filter(Boolean),a=i.filter(s=>!e.includes(s.id));O([...s,...a])}else O([...i])}catch(e){A(`Error loading data: ${e.message}`),w(null),D([])}finally{_(!1),$(!1),L(!1)}},[n,u,x]),(0,r.useCallback)(async e=>{if(n&&e){V(!0),Y(null),es(null);try{let s=await fetch(`/api/custom-configs/${n}/keys/${e}/complexity-assignments`);if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to fetch complexity assignments")}let a=await s.json();z(s=>({...s,[e]:a})),Z(a)}catch(e){Y(`Error fetching assignments for key: ${e.message}`),Z([])}finally{V(!1)}}},[n]);let ea=(e,s)=>{Z(a=>s?[...a,e].sort((e,s)=>e-s):a.filter(s=>s!==e))},et=(0,r.useCallback)(async()=>{if(!n||!W)return void Y("No API key selected to save assignments for.");X(!0),Y(null),es(null);try{let e=await fetch(`/api/custom-configs/${n}/keys/${W}/complexity-assignments`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({complexity_levels:G})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save complexity assignments")}let s=await e.json();z(e=>({...e,[W]:[...G]})),es(s.message||"Complexity assignments saved successfully!")}catch(e){Y(`Error saving assignments: ${e.message}`)}finally{X(!1)}},[n,W,G]),er=(e,s)=>{let a=[...q],t=a[e];"up"===s&&e>0?(a.splice(e,1),a.splice(e-1,0,t)):"down"===s&&e<a.length-1&&(a.splice(e,1),a.splice(e+1,0,t)),O(a),K({ordered_api_key_ids:a.map(e=>e.id)})},ei=async e=>{if(e.preventDefault(),!n||!N)return void A("Configuration details not loaded.");_(!0),A(null),P(null);let s=I;"strict_fallback"===M&&(s={ordered_api_key_ids:q.map(e=>e.id)});try{let e=await fetch(`/api/custom-configs/${n}/routing`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({routing_strategy:M,routing_strategy_params:s})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save routing settings")}let a=await e.json();P(a.message||"Routing settings saved successfully!"),w(e=>e?{...e,routing_strategy:M,routing_strategy_params:s}:null),K(s)}catch(e){A(`Error saving settings: ${e.message}`)}finally{_(!1)}},en=()=>"complexity_round_robin"!==M?null:(0,t.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Complexity-Based Key Assignments"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity."}),F&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading API keys..."})]}),!F&&0===B.length&&(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-yellow-800",children:"No API keys found for this configuration. Please add API keys first on the model configuration page."})}),B.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{htmlFor:"apiKeyForComplexity",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Key to Assign Complexities:"}),(0,t.jsxs)("select",{id:"apiKeyForComplexity",value:W||"",onChange:e=>H(e.target.value||null),className:"form-select max-w-md",children:[(0,t.jsx)("option",{value:"",disabled:!0,children:"-- Select an API Key --"}),B.map(e=>(0,t.jsxs)("option",{value:e.id,children:[e.label," (",e.provider," - ",e.predefined_model_id,")"]},e.id))]})]}),W&&(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsxs)("h4",{className:"text-md font-medium text-gray-900 mb-4",children:["Assign Complexity Levels for: ",(0,t.jsx)("span",{className:"text-orange-600",children:B.find(e=>e.id===W)?.label})]}),U&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading current assignments..."})]}),Q&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,t.jsx)("p",{className:"text-red-800 text-sm",children:Q})}),ee&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,t.jsx)("p",{className:"text-green-800 text-sm",children:ee})}),!U&&(0,t.jsx)("div",{className:"space-y-3 mb-6",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200",children:[(0,t.jsx)("input",{type:"checkbox",checked:G.includes(e),onChange:s=>ea(e,s.target.checked),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"}),(0,t.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["Complexity Level ",e]})]},e))}),(0,t.jsx)("button",{onClick:et,disabled:J||U,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:J?"Saving Assignments...":"Save Assignments for this Key"})]})]});return R&&!x(n)?(0,t.jsx)(b,{}):k&&!N?(0,t.jsx)(j,{}):!C||N||k?(0,t.jsx)("div",{className:"min-h-screen bg-cream",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("button",{onClick:()=>{s.push(y())},className:"btn-secondary inline-flex items-center text-sm",children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),(()=>{let e=a.get("from");return"routing-setup"===e?"Back to Routing Setup":"Back to Configuration"})()]})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("h1",{className:"text-h1 text-gray-900",children:"Advanced Routing Setup"}),N&&(0,t.jsxs)("p",{className:"text-body-sm text-gray-600 mt-1",children:["Configuration: ",(0,t.jsx)("span",{className:"text-orange-600 font-semibold",children:N.name})]})]})]})}),C&&!S&&(0,t.jsx)("div",{className:"card border-red-200 bg-red-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-red-800",children:"Configuration Error"}),(0,t.jsx)("p",{className:"text-red-700 mt-1",children:C})]})]})}),S&&(0,t.jsx)("div",{className:"card border-green-200 bg-green-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-green-800",children:"Settings Saved"}),(0,t.jsx)("p",{className:"text-green-700 mt-1",children:S})]})]})}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6 sticky top-8",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Routing Strategy"}),(0,t.jsx)("div",{className:"space-y-3",children:f.map(e=>{let s=e.icon,a=M===e.id;return(0,t.jsx)("button",{onClick:()=>{if(E(e.id),"strict_fallback"===e.id){let e=I.ordered_api_key_ids;e&&Array.isArray(e)?O([...e.map(e=>B.find(s=>s.id===e)).filter(Boolean),...B.filter(s=>!e.includes(s.id))]):O([...B]),K({ordered_api_key_ids:q.map(e=>e.id)})}else K({}),O([...B]);H(null),Z([]),Y(null),es(null)},disabled:k,className:`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group ${a?"border-orange-500 bg-orange-50 shadow-lg transform scale-[1.02]":"border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25 hover:shadow-md"}`,children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:`p-3 rounded-lg transition-colors duration-300 ${a?"bg-orange-100 text-orange-600":"bg-gray-100 text-gray-600 group-hover:bg-orange-100 group-hover:text-orange-600"}`,children:(0,t.jsx)(s,{className:"w-6 h-6"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("h3",{className:`font-semibold text-sm transition-colors duration-300 ${a?"text-orange-900":"text-gray-900"}`,children:e.name}),a&&(0,t.jsx)(h.A,{className:"w-4 h-4 text-orange-600 animate-in fade-in duration-300"})]}),(0,t.jsx)("p",{className:`text-xs leading-relaxed transition-colors duration-300 ${a?"text-orange-700":"text-gray-600"}`,children:e.shortDescription})]})]})},e.id)})})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsx)("form",{onSubmit:ei,children:(0,t.jsx)("div",{className:"card p-8 min-h-[600px]",children:(0,t.jsx)("div",{className:"animate-in fade-in slide-in-from-right-4 duration-500",children:(()=>{let e=f.find(e=>e.id===M);return"none"===M?(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)(d.A,{className:"w-10 h-10 text-orange-600"})}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Default Behavior"}),(0,t.jsx)("p",{className:"text-gray-600 max-w-md mx-auto leading-relaxed",children:e?.description}),(0,t.jsx)("div",{className:"mt-8 p-4 bg-green-50 border border-green-200 rounded-xl",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(h.A,{className:"w-5 h-5 text-green-600"}),(0,t.jsx)("span",{className:"text-green-800 font-medium",children:"No additional setup required"})]})}),(0,t.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200",children:(0,t.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:k,children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"intelligent_role"===M?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(o.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Intelligent Role Routing"]}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e?.description})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-900 mb-3",children:"How it works:"}),(0,t.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,t.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"1"})}),(0,t.jsx)("p",{children:"System analyzes your prompt to understand the main task"})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,t.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"2"})}),(0,t.jsx)("p",{children:"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')"})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,t.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"3"})}),(0,t.jsx)("p",{children:"Routes to assigned API key or falls back to 'Default General Chat Model'"})]})]})]}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(h.A,{className:"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Ready to use!"}),(0,t.jsx)("p",{className:"text-sm text-green-800 leading-relaxed",children:"No additional setup required. Future enhancements may allow further customization."})]})]})})]}),(0,t.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,t.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:k,children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"strict_fallback"===M?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(m,{className:"w-7 h-7 mr-3 text-orange-600"}),"Strict Fallback Configuration"]}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e?.description})]}),F&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"w-8 h-8 border-2 border-orange-600/20 border-t-orange-600 rounded-full animate-spin"}),(0,t.jsx)("p",{className:"text-gray-600 ml-3",children:"Loading API keys..."})]}),!F&&0===B.length&&(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,t.jsx)("h4",{className:"text-lg font-semibold text-yellow-900 mb-2",children:"No API Keys Found"}),(0,t.jsx)("p",{className:"text-yellow-800 leading-relaxed",children:"Please add API keys on the main configuration page to set up fallback order."})]}),!F&&B.length>0&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on."})]})}),(0,t.jsx)("ul",{className:"space-y-3",children:q.map((e,s)=>(0,t.jsx)(v,{apiKey:e,index:s,onMoveUp:s>0?()=>er(s,"up"):void 0,onMoveDown:s<q.length-1?()=>er(s,"down"):void 0},e.id))})]}),(0,t.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,t.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:k||0===B.length,children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"complexity_round_robin"===M?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(c.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Complexity-Based Round-Robin"]}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e?.description})]}),en(),(0,t.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,t.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:k,children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):null})()})})})})]})})]})}):(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Routing Setup Error"}),(0,t.jsxs)("div",{className:"card border-red-200 bg-red-50 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,t.jsx)("p",{className:"text-red-800",children:C})]}),(0,t.jsx)(l(),{href:"/my-models",className:"mt-4 btn-primary inline-block",children:"Back to My Models"})]})]})}},54984:(e,s,a)=>{Promise.resolve().then(a.bind(a,47417))},55296:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(43210);let r=t.forwardRef(function({title:e,titleId:s,...a},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},a),e?t.createElement("title",{id:s},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},58089:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(43210);let r=t.forwardRef(function({title:e,titleId:s,...a},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},a),e?t.createElement("title",{id:s},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},60925:(e,s,a)=>{"use strict";a.d(s,{c:()=>i});var t=a(43210);let r={};function i(){let[e,s]=(0,t.useState)({}),a=(0,t.useRef)({}),i=(0,t.useCallback)(e=>{let s=r[e];return!!s&&!(Date.now()-s.timestamp>3e5)&&!s.isLoading},[]),n=(0,t.useCallback)(e=>{let s=r[e];return!s||s.isLoading?null:Date.now()-s.timestamp>3e5?(delete r[e],null):s.data},[]),l=(0,t.useCallback)(async(e,t="medium")=>{if(i(e))return n(e);if(r[e]?.isLoading)return null;a.current[e]&&a.current[e].abort();let l=new AbortController;a.current[e]=l,r[e]={data:{},timestamp:Date.now(),isLoading:!0},s(s=>({...s,[e]:"loading"}));try{"low"===t?await new Promise(e=>setTimeout(e,200)):"medium"===t&&await new Promise(e=>setTimeout(e,50));let[a,i,n]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:l.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:l.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:l.signal})]),d=null,o=[],c="none",m={},u=[];"fulfilled"===a.status&&a.value.ok&&(c=(d=await a.value.json()).routing_strategy||"none",m=d.routing_strategy_params||{}),"fulfilled"===i.status&&i.value.ok&&(o=await i.value.json()),"fulfilled"===n.status&&n.value.ok&&(u=await n.value.json());let x={configDetails:d,apiKeys:o,routingStrategy:c,routingParams:m,complexityAssignments:u};return r[e]={data:x,timestamp:Date.now(),isLoading:!1},s(s=>({...s,[e]:"success"})),x}catch(a){if("AbortError"===a.name)return null;return delete r[e],s(s=>({...s,[e]:"error"})),null}finally{delete a.current[e]}},[i,n]),d=(0,t.useCallback)(e=>({onMouseEnter:()=>{i(e)||l(e,"high")}}),[l,i]),o=(0,t.useCallback)(e=>{delete r[e],s(s=>{let a={...s};return delete a[e],a})},[]),c=(0,t.useCallback)(()=>{Object.keys(r).forEach(e=>{delete r[e]}),s({})},[]);return{prefetchRoutingSetupData:l,getCachedData:n,isCached:i,createHoverPrefetch:d,clearCache:o,clearAllCache:c,getStatus:(0,t.useCallback)(s=>e[s]||"idle",[e]),getCacheInfo:(0,t.useCallback)(()=>({cachedConfigs:Object.keys(r),cacheSize:Object.keys(r).length,totalCacheAge:Object.values(r).reduce((e,s)=>e+(Date.now()-s.timestamp),0)/Object.keys(r).length}),[]),prefetchStatus:e}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67064:(e,s,a)=>{Promise.resolve().then(a.bind(a,52074))},70149:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(43210);let r=t.forwardRef(function({title:e,titleId:s,...a},r){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},a),e?t.createElement("title",{id:s},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},71238:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});var t=a(37413),r=a(47417);function i(){return(0,t.jsx)(r.RoutingSetupSkeleton,{})}},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8153,1658,4947,8947,2646],()=>a(25333));module.exports=t})();