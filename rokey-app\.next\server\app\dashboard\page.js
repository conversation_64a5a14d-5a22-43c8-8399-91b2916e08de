(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16405:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=a(65239),r=a(48088),n=a(88170),i=a.n(n),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,80559)),"C:\\RoKey App\\rokey-app\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\dashboard\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},45700:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},50515:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},51385:(e,t,a)=>{Promise.resolve().then(a.bind(a,80559))},57891:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58061:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(60687),r=a(43210),n=a(16189),i=a(45994),l=a(31082),o=a(58089),d=a(68589),c=a(45700),m=a(57891),u=a(86297),h=a(61245),p=a(50515),x=a(59168);function g(){let e=(0,n.useRouter)(),[t,a]=(0,r.useState)(null),[g,v]=(0,r.useState)(!1),[y,f]=(0,r.useState)(!0),[w,j]=(0,r.useState)(null),[b,N]=(0,r.useState)([]),[k,A]=(0,r.useState)([{name:"API Gateway",status:"operational"},{name:"Routing Engine",status:"operational"},{name:"Analytics",status:"degraded"}]),C=async()=>{try{y&&v(!0);let e=new Date;e.setDate(e.getDate()-30);let t=await fetch(`/api/analytics/summary?startDate=${e.toISOString()}&groupBy=day`);if(!t.ok)throw Error("Failed to fetch analytics data");let s=await t.json();a(s)}catch(e){j(e.message)}finally{y&&v(!1)}},E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(e),_=e=>new Intl.NumberFormat("en-US").format(e),L=async()=>{try{let e=await fetch("/api/activity?limit=10");if(!e.ok)throw Error("Failed to fetch recent activity");let t=(await e.json()).activities.map(e=>({id:e.id,action:e.action,model:e.model,time:e.time,status:e.status,details:e.details}));N(t)}catch(e){N([{id:"1",action:"System initialized",model:"RoKey",time:"Just now",status:"info"}])}},R=t?[{name:"Total Requests",value:_(t.summary.total_requests),change:"Last 30 days",changeType:"neutral",icon:i.A},{name:"Total Cost",value:E(t.summary.total_cost),change:`${E(t.summary.average_cost_per_request)} avg/request`,changeType:"neutral",icon:l.A},{name:"Success Rate",value:`${t.summary.success_rate.toFixed(1)}%`,change:`${_(t.summary.successful_requests)} successful`,changeType:t.summary.success_rate>=95?"positive":"negative",icon:o.A},{name:"Total Tokens",value:_(t.summary.total_tokens),change:`${_(t.summary.total_input_tokens)} in, ${_(t.summary.total_output_tokens)} out`,changeType:"neutral",icon:d.A}]:[];return g&&y?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},t))})]}):w?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"animate-slide-in",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Welcome back! \uD83D\uDC4B"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,s.jsxs)("div",{className:"card p-6 text-center",children:[(0,s.jsxs)("p",{className:"text-red-600 mb-4",children:["Error loading analytics data: ",w]}),(0,s.jsx)("button",{onClick:C,className:"btn-primary",children:"Retry"})]})]}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"animate-slide-in",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Welcome back! \uD83D\uDC4B"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in",children:R.map((e,t)=>(0,s.jsx)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200",style:{animationDelay:`${100*t}ms`},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.name}),(0,s.jsx)("p",{className:"text-3xl font-bold text-gray-900 mt-2",children:e.value}),(0,s.jsxs)("p",{className:`text-sm mt-2 flex items-center ${"positive"===e.changeType?"text-green-600":"negative"===e.changeType?"text-red-600":"text-gray-500"}`,children:["neutral"!==e.changeType&&(0,s.jsx)(c.A,{className:`h-4 w-4 mr-1 ${"negative"===e.changeType?"rotate-180":""}`}),e.change]})]}),(0,s.jsx)("div",{className:"p-3 rounded-lg bg-orange-50",children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-orange-600"})})]})},e.name))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,s.jsxs)("div",{className:"card p-6 animate-slide-in",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{onClick:()=>{e.push("/my-models")},className:"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 mr-3"}),"Add New Model"]}),(0,s.jsxs)("button",{onClick:()=>{e.push("/playground")},className:"btn-secondary w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 mr-3"}),"Test in Playground"]}),(0,s.jsxs)("button",{onClick:()=>{e.push("/logs")},className:"btn-outline w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 mr-3"}),"View Logs"]})]})]}),(0,s.jsxs)("div",{className:"card p-6 animate-slide-in",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"System Status"}),(0,s.jsx)("div",{className:"space-y-4",children:k.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full mr-3 ${"operational"===e.status?"bg-green-500":"degraded"===e.status?"bg-yellow-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-gray-700",children:e.name})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("span",{className:`text-sm font-medium ${"operational"===e.status?"text-green-600":"degraded"===e.status?"text-yellow-600":"text-red-600"}`,children:"operational"===e.status?"Operational":"degraded"===e.status?"Degraded":"Down"}),e.lastChecked&&(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.lastChecked})]})]},e.name))})]})]}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"card p-6 animate-slide-in",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Recent Activity"}),(0,s.jsx)("button",{onClick:L,className:"text-orange-600 hover:text-orange-700 text-sm font-medium",children:"Refresh"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[0===b.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"No recent activity"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Activity will appear here as you use the API"})]}):b.slice(-4).map(e=>(0,s.jsxs)("div",{className:"flex items-start p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 group overflow-hidden",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full mr-4 ${"success"===e.status?"bg-green-500":"warning"===e.status?"bg-yellow-500":"error"===e.status?"bg-red-500":"bg-blue-500"}`}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-gray-900 font-medium break-words",children:e.action}),(0,s.jsxs)("p",{className:"text-gray-600 text-sm break-words",children:[e.model," • ",e.time]}),e.details&&(0,s.jsx)("p",{className:"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed",title:e.details,children:e.details})]}),(0,s.jsx)("div",{className:"text-gray-500 group-hover:text-gray-700",children:"error"===e.status?(0,s.jsx)(x.A,{className:"h-5 w-5 text-red-500"}):(0,s.jsx)(d.A,{className:"h-5 w-5"})})]},e.id)),b.length>4&&(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,s.jsxs)("button",{onClick:()=>{window.location.href="/logs"},className:"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors",children:["View All Activity (",b.length,")",(0,s.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]})})]})]})}},58089:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},59168:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68589:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(43210);let r=s.forwardRef(function({title:e,titleId:t,...a},r){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},a),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))})},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\dashboard\\page.tsx","default")},87833:(e,t,a)=>{Promise.resolve().then(a.bind(a,58061))}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,8153,1658,8947],()=>a(16405));module.exports=s})();