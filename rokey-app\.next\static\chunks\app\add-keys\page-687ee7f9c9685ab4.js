(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5833],{38152:(e,r,t)=>{"use strict";t.d(r,{Pi:()=>o.A,fK:()=>a.A,uc:()=>n.A});var o=t(55628),n=t(31151),a=t(74500)},75922:(e,r,t)=>{"use strict";t.d(r,{MG:()=>o});let o=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76805:(e,r,t)=>{Promise.resolve().then(t.bind(t,81767))},81767:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var o=t(75922),n=t(80377),a=t(87162);let c=o.MG.map(e=>({value:e.id,label:e.name})),d=e=>{let r=o.MG.find(r=>r.id===e);return r?r.models.map(e=>({value:e.id,label:e.name})):[]};function i(){var e;let r=(0,a.Z)(),[t,o]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())((null==(e=c[0])?void 0:e.value)||"openai"),[i,l]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[u,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[O,m]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[f,h]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[N,b]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[_,p]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[D,E]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[U,v]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[j,x]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),w=async()=>{v(!0);let e=null;try{let e=await fetch("/api/keys");if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to fetch keys")}let r=await e.json();E(r),b(null)}catch(e){b("Error fetching keys: ".concat(e.message))}v(!1)};Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{w()},[]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{var e;l((null==(e=d(t)[0])?void 0:e.value)||"")},[t]);let y=async e=>{e.preventDefault(),h(!0),b(null),p(null);try{var r;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:t,predefined_model_id:i,api_key_raw:u,label:O,custom_api_config_id:""})}),n=await e.json();if(!e.ok)throw Error(n.details||n.error||"Failed to save API key");p('API key "'.concat(O,'" saved successfully!')),o((null==(r=c[0])?void 0:r.value)||"openai"),l(""),s(""),m(""),await w()}catch(e){b(e.message),p(null)}h(!1)},g=(e,t)=>{r.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(t,'"? This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{x(e),b(null),p(null);try{let r=await fetch("/api/keys/".concat(e),{method:"DELETE"}),o=await r.json();if(!r.ok)throw Error(o.details||o.error||"Failed to delete API key");p('API key "'.concat(t,'" deleted successfully!')),E(r=>r.filter(r=>r.id!==e))}catch(e){throw b(e.message),p(null),e}finally{x(null)}})},C=d(t);return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:"text-3xl font-bold mb-6",children:"Add API Key"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("form",{onSubmit:y,className:"space-y-6 bg-gray-800 p-6 rounded-lg shadow-xl max-w-lg",children:[N&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Error: ",N]}),_&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-green-400 bg-green-900/50 p-3 rounded-md",children:_}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-1",children:"Provider"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"provider",value:t,onChange:e=>o(e.target.value),className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",children:c.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"modelId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Model ID"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"modelId",value:i,onChange:e=>l(e.target.value),disabled:!C.length,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50",children:C.length>0?C.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value)):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",disabled:!0,children:"Select a provider first or no models configured"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"apiKey",className:"block text-sm font-medium text-gray-300 mb-1",children:"API Key"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"password",id:"apiKey",value:u,onChange:e=>s(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your API key"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-1",children:"Label"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",id:"label",value:O,onChange:e=>m(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., My Personal OpenAI Key"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"submit",disabled:f,className:"w-full px-4 py-2.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 disabled:opacity-50 font-medium",children:f?"Saving...":"Save API Key"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:"text-2xl font-bold mb-4",children:"Saved API Keys"}),U?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-400",children:"Loading keys..."}):N&&0===D.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Could not load keys: ",N.replace("Error fetching keys: ","")]}):0===D.length?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-400",children:"No API keys saved yet."}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"overflow-x-auto bg-gray-800 p-4 rounded-lg shadow-xl",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("table",{className:"min-w-full text-sm text-left text-gray-300",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("thead",{className:"text-xs text-gray-400 uppercase bg-gray-700",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("tr",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:"Label"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:"Provider"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:"Predefined Model ID"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:"Status"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:"Created At"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:"Last Used"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("th",{scope:"col",className:"px-6 py-3",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"sr-only",children:"Actions"})})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("tbody",{children:D.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("tr",{className:"border-b border-gray-700 hover:bg-gray-700/50",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4 font-medium whitespace-nowrap text-white",children:e.label}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4",children:e.provider}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4",children:e.predefined_model_id}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"px-2 py-1 text-xs font-medium rounded-full \n                        ".concat("active"===e.status?"bg-green-900 text-green-300":"inactive"===e.status?"bg-yellow-900 text-yellow-300":"bg-red-900 text-red-300"),children:e.status})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4",children:new Date(e.created_at).toLocaleDateString()}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4",children:e.last_used_at?new Date(e.last_used_at).toLocaleDateString():"Never"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("td",{className:"px-6 py-4 text-right",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>g(e.id,e.label),disabled:j===e.id,className:"font-medium text-red-500 hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed",children:j===e.id?"Deleting...":"Delete"})})]},e.id))})]})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(n.A,{isOpen:r.isOpen,onClose:r.hideConfirmation,onConfirm:r.onConfirm,title:r.title,message:r.message,confirmText:r.confirmText,cancelText:r.cancelText,type:r.type,isLoading:r.isLoading})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[274,5738,1486,2662,8669,8848,4696,9173,6642,7706,7544,2138,8899,5495,7358],()=>r(76805)),_N_E=e.O()}]);