"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1486],{78:(t,e,n)=>{n.d(e,{w:()=>S});var i=n(52290),s=n(21448),o=n(43891),r=n(66698),a=n(51442),l=n(26953),u=n(51586),h=n(78588),c=n(64200),m=n(81786),d=n(94198),p=n(33757),v=n(68212),f=n(33991),g=n(76333),y=n(61665);function x(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function P(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function A(t,e,n){return{min:E(t,e),max:E(t,n)}}function E(t,e){return"number"==typeof t?t:t[e]||0}let C=new WeakMap;class w{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,m.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new y.Q(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,u.e)(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,o.Wp)(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,d.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(o.rq.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[t];i&&(e=(0,c.CQ)(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&o.Gt.postRender(()=>s(t,e)),(0,g.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:r}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(r),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,d.X)(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:(0,v.s)(this.visualElement)})}stop(t,e){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:s}=this.getProps();s&&o.Gt.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:i}=this.getProps();if(!n||!M(t,i,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?(0,o.k$)(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?(0,o.k$)(n,t,i.max):Math.min(t,n)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&(0,f.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:i,right:s}){return{x:x(t.x,n,s),y:x(t.y,e,i)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:A(t,"left","right"),y:A(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&(0,d.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!(0,f.X)(e))return!1;let i=e.current;(0,s.V1)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let r=(0,p.L)(i,o.root,this.visualElement.getTransformPagePoint()),a=(t=o.layout.layoutBox,{x:P(t.x,r.x),y:P(t.y,r.y)});if(n){let t=n((0,h.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,h.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{};return Promise.all((0,d.X)(r=>{if(!M(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[r]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,u)})).then(r)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return(0,g.g)(this.visualElement,t),n.start((0,r.f)(t,n,0,e,this.visualElement,!1))}stopAnimation(){(0,d.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,d.X)(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){(0,d.X)(e=>{let{drag:n}=this.getProps();if(!M(e,n,this.currentDirection))return;let{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){let{min:n,max:r}=i.layout.layoutBox[e];s.set(t[e]-(0,o.k$)(n,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!(0,f.X)(e)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};(0,d.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();i[t]=function(t,e){let n=.5,i=(0,c.CQ)(t),o=(0,c.CQ)(e);return o>i?n=(0,s.qB)(e.min,e.max-i,t.min):i>o&&(n=(0,s.qB)(t.min,t.max-o,e.min)),(0,s.qE)(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),(0,d.X)(e=>{if(!M(e,t,null))return;let n=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];n.set((0,o.k$)(s,r,i[e]))})}addListeners(){if(!this.visualElement.current)return;C.set(this.visualElement,this);let t=this.visualElement.current,e=(0,l.h)(t,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),n=()=>{let{dragConstraints:t}=this.getProps();(0,f.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",n);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),o.Gt.read(n);let r=(0,a.k)(window,"resize",()=>this.scalePositionWithinConstraints()),u=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,d.X)(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),u&&u()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=.35,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function M(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class S extends i.X{constructor(t){super(t),this.removeGroupControls=s.lQ,this.removeListeners=s.lQ,this.controls=new w(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||s.lQ}unmount(){this.removeGroupControls(),this.removeListeners()}}},198:(t,e,n)=>{n(21448),n(18802),n(78660)},1265:(t,e,n)=>{n(43891),n(21448),n(31788),n(46926);let i=new Set},2736:(t,e,n)=>{n(95155),n(21448),n(12115),n(82885),n(43050)},2999:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(12115).createContext)({})},5910:(t,e,n)=>{n.d(e,{p:()=>i});let i=t=>Array.isArray(t)},6340:(t,e,n)=>{n.d(e,{N:()=>i});function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},16242:(t,e,n)=>{n(82885);class i{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}},19209:(t,e,n)=>{n(95155),n(21448),n(12115),n(36545),n(82885),n(43891);n(8619),n(62094)},19578:(t,e,n)=>{n.d(e,{$:()=>l});var i=n(43891),s=n(18802),o=n(76333),r=n(46926),a=n(66698);function l(t,e,{delay:n=0,transitionOverride:u,type:h}={}){let{transition:c=t.getDefaultTransition(),transitionEnd:m,...d}=e;u&&(c=u);let p=[],v=h&&t.animationState&&t.animationState.getState()[h];for(let e in d){let s=t.getValue(e,t.latestValues[e]??null),l=d[e];if(void 0===l||v&&function({protectedKeys:t,needsAnimating:e},n){let i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}(v,e))continue;let u={delay:n,...(0,i.rU)(c||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(l)&&l===h&&!u.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let n=(0,r.P)(t);if(n){let t=window.MotionHandoffAnimation(n,e,i.Gt);null!==t&&(u.startTime=t,m=!0)}}(0,o.g)(t,e),s.start((0,a.f)(e,s,l,t.shouldReduceMotion&&i.$y.has(e)?{type:!1}:u,t,m));let f=s.animation;f&&p.push(f)}return m&&Promise.all(p).then(()=>{i.Gt.update(()=>{m&&(0,s.U)(t,m)})}),p}},19624:(t,e,n)=>{n.d(e,{c:()=>r});var i=n(21448),s=n(51442),o=n(52290);class r extends o.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,i.Fs)((0,s.k)(this.node.current,"focus",()=>this.onFocus()),(0,s.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},19726:(t,e,n)=>{n.d(e,{e:()=>a});var i=n(43891),s=n(51586),o=n(52290);function r(t,e,n){let{props:o}=t;t.animationState&&o.whileHover&&t.animationState.setActive("whileHover","Start"===n);let r=o["onHover"+n];r&&i.Gt.postRender(()=>r(e,(0,s.e)(e)))}class a extends o.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.PT)(t,(t,e)=>(r(this.node,e,"Start"),t=>r(this.node,t,"End"))))}unmount(){}}},24132:(t,e,n)=>{n.d(e,{z:()=>a});var i=n(12115),s=n(2999),o=n(19253),r=n(65305);function a(t){let{initial:e,animate:n}=function(t,e){if((0,o.e)(t)){let{initial:e,animate:n}=t;return{initial:!1===e||(0,r.w)(e)?e:void 0,animate:(0,r.w)(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(s.A));return(0,i.useMemo)(()=>({initial:e,animate:n}),[l(e),l(n)])}function l(t){return Array.isArray(t)?t.join(" "):t}},25214:(t,e,n)=>{n.d(e,{Y:()=>i});let i=(0,n(12115).createContext)({strict:!1})},26953:(t,e,n)=>{n.d(e,{h:()=>o});var i=n(51442),s=n(51586);function o(t,e,n,o){return(0,i.k)(t,e,(0,s.F)(n),o)}},31788:(t,e,n)=>{n.d(e,{n:()=>i});let i="data-"+(0,n(78450).I)("framerAppearId")},32082:(t,e,n)=>{n.d(e,{xQ:()=>o});var i=n(12115),s=n(80845);function o(t=!0){let e=(0,i.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:r,register:a}=e,l=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,i.useCallback)(()=>t&&r&&r(l),[l,r,t]);return!n&&r?[!1,u]:[!0]}},35580:(t,e,n)=>{n.d(e,{z:()=>o});var i=n(43891),s=n(66698);function o(t,e,n){let o=(0,i.SS)(t)?t:(0,i.OQ)(t);return o.start((0,s.f)("",o,e,n)),o.animation}},36464:(t,e,n)=>{var i=n(43891),s=n(21448);function o(t){return"object"==typeof t&&!Array.isArray(t)}let r=t=>"number"==typeof t;var a=n(65511),l=n(19578),u=n(75245),h=n(13513),c=n(60728);function m(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=(0,i.xZ)(t)&&!(0,i.h1)(t)?new c.l(e):new u.M(e);n.mount(t),a.C.set(t,n)}function d(t){let e=new h.K({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),a.C.set(t,e)}var p=n(35580)},38160:(t,e,n)=>{n.d(e,{f:()=>h});var i=n(43891),s=n(21448),o=n(26953),r=n(52290),a=n(68212),l=n(61665);let u=t=>(e,n)=>{t&&i.Gt.postRender(()=>t(e,n))};class h extends r.X{constructor(){super(...arguments),this.removePointerDownListener=s.lQ}onPointerDown(t){this.session=new l.Q(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:(0,a.s)(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:s}=this.node.getProps();return{onSessionStart:u(t),onStart:u(e),onMove:n,onEnd:(t,e)=>{delete this.session,s&&i.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=(0,o.h)(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},39126:(t,e,n)=>{(0,n(12115).createContext)(null)},43050:(t,e,n)=>{n(95155),n(12115),n(90869),n(39126),n(39174),n(80131)},46926:(t,e,n)=>{n.d(e,{P:()=>s});var i=n(31788);function s(t){return t.props[i.n]}},49441:(t,e,n)=>{n.d(e,{H:()=>a});var i=n(43891),s=n(51586),o=n(52290);function r(t,e,n){let{props:o}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&o.whileTap&&t.animationState.setActive("whileTap","Start"===n);let r=o["onTap"+("End"===n?"":n)];r&&i.Gt.postRender(()=>r(e,(0,s.e)(e)))}class a extends o.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.c$)(t,(t,e)=>(r(this.node,e,"Start"),(t,{success:e})=>r(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},49489:(t,e,n)=>{n(21448)},51251:(t,e,n)=>{n(95155),n(12115),n(25214),n(9480)},51442:(t,e,n)=>{n.d(e,{k:()=>i});function i(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}},51508:(t,e,n)=>{n.d(e,{Q:()=>i});let i=(0,n(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},51586:(t,e,n)=>{n.d(e,{F:()=>o,e:()=>s});var i=n(43891);function s(t){return{point:{x:t.pageX,y:t.pageY}}}let o=t=>e=>(0,i.Mc)(e)&&t(e,s(e))},55539:(t,e,n)=>{n(43891),n(21448)},56787:(t,e,n)=>{n(95155),n(12115),n(51508),n(99776),n(82885)},60760:(t,e,n)=>{n(95155);var i=n(12115);n(90869),n(82885),n(97494),n(80845);var s=n(43891);n(51508),i.Component,n(32082)},61665:(t,e,n)=>{n.d(e,{Q:()=>l});var i=n(43891),s=n(21448),o=n(26953),r=n(51586),a=n(2986);class l{constructor(t,e,{transformPagePoint:n,contextWindow:l,dragSnapToOrigin:h=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=c(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=(0,a.w)(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;let{point:s}=t,{timestamp:o}=i.uv;this.history.push({...s,timestamp:o});let{onStart:r,onMove:l}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=u(e,this.transformPagePoint),i.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=c("pointercancel"===t.type?this.lastMoveEventInfo:u(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!(0,i.Mc)(t))return;this.dragSnapToOrigin=h,this.handlers=e,this.transformPagePoint=n,this.contextWindow=l||window;let m=u((0,r.e)(t),this.transformPagePoint),{point:d}=m,{timestamp:p}=i.uv;this.history=[{...d,timestamp:p}];let{onSessionStart:v}=e;v&&v(t,c(m,this.history)),this.removeListeners=(0,s.Fs)((0,o.h)(this.contextWindow,"pointermove",this.handlePointerMove),(0,o.h)(this.contextWindow,"pointerup",this.handlePointerUp),(0,o.h)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,i.WG)(this.updatePoint)}}function u(t,e){return e?{point:e(t.point)}:t}function h(t,e){return{x:t.x-e.x,y:t.y-e.y}}function c({point:t},e){return{point:t,delta:h(t,m(e)),offset:h(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null,o=m(t);for(;n>=0&&(i=t[n],!(o.timestamp-i.timestamp>(0,s.fD)(.1)));)n--;if(!i)return{x:0,y:0};let r=(0,s.Xu)(o.timestamp-i.timestamp);if(0===r)return{x:0,y:0};let a={x:(o.x-i.x)/r,y:(o.y-i.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function m(t){return t[t.length-1]}},66698:(t,e,n)=>{n.d(e,{f:()=>c});var i=n(43891),s=n(21448);let o=t=>null!==t,r={type:"spring",stiffness:500,damping:25,restSpeed:10},a=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),l={type:"keyframes",duration:.8},u={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},h=(t,{keyframes:e})=>e.length>2?l:i.fu.has(t)?t.startsWith("scale")?a(e[1]):r:u,c=(t,e,n,r={},a,l)=>u=>{let c=(0,i.rU)(r,t)||{},m=c.delay||r.delay||0,{elapsed:d=0}=r;d-=(0,s.fD)(m);let p={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...c,delay:-d,onUpdate:t=>{e.set(t),c.onUpdate&&c.onUpdate(t)},onComplete:()=>{u(),c.onComplete&&c.onComplete()},name:t,motionValue:e,element:l?void 0:a};!function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(c)&&Object.assign(p,h(t,p)),p.duration&&(p.duration=(0,s.fD)(p.duration)),p.repeatDelay&&(p.repeatDelay=(0,s.fD)(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let v=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(v=!0)),(s.W9.instantAnimations||s.W9.skipAnimations)&&(v=!0,p.duration=0,p.delay=0),p.allowFlatten=!c.type&&!c.ease,v&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},i){let s=t.filter(o),r=e&&"loop"!==n&&e%2==1?0:s.length-1;return s[r]}(p.keyframes,c);if(void 0!==t)return void i.Gt.update(()=>{p.onUpdate(t),p.onComplete()})}return c.isSync?new i.sb(p):new i.AT(p)}},70797:(t,e,n)=>{n.d(e,{N:()=>i});let i=(0,n(12115).createContext)({})},71492:(t,e,n)=>{n(82885),n(86811),n(36464)},78660:(t,e,n)=>{n.d(e,{_:()=>a});var i=n(20419),s=n(19578);function o(t,e,n={}){let a=(0,i.K)(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:l=t.getDefaultTransition()||{}}=a||{};n.transitionOverride&&(l=n.transitionOverride);let u=a?()=>Promise.all((0,s.$)(t,a,n)):()=>Promise.resolve(),h=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:u}=l;return function(t,e,n=0,i=0,s=1,a){let l=[],u=(t.variantChildren.size-1)*i,h=1===s?(t=0)=>t*i:(t=0)=>u-t*i;return Array.from(t.variantChildren).sort(r).forEach((t,i)=>{t.notify("AnimationStart",e),l.push(o(t,e,{...a,delay:n+h(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(l)}(t,e,s+i,a,u,n)}:()=>Promise.resolve(),{when:c}=l;if(!c)return Promise.all([u(),h(n.delay)]);{let[t,e]="beforeChildren"===c?[u,h]:[h,u];return t().then(()=>e())}}function r(t,e){return t.sortNodePosition(e)}function a(t,e,n={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>o(t,e,n)));else if("string"==typeof e)r=o(t,e,n);else{let o="function"==typeof e?(0,i.K)(t,e,n.custom):e;r=Promise.all((0,s.$)(t,o,n))}return r.then(()=>{t.notify("AnimationComplete",e)})}},80845:(t,e,n)=>{n.d(e,{t:()=>i});let i=(0,n(12115).createContext)(null)},88558:(t,e,n)=>{n(12115);var i=n(96488),s=n(81786),o=n(40956);n(82885),n(78660);let r=()=>({});o.B,(0,i.T)({scrapeMotionValuesFromProps:r,createRenderState:r})},90693:(t,e,n)=>{n(12115),n(80845)},90869:(t,e,n)=>{n.d(e,{L:()=>i});let i=(0,n(12115).createContext)({})},93810:(t,e,n)=>{n(12115),n(51442)},98663:(t,e,n)=>{n(82885),n(97494),n(198)},98828:(t,e,n)=>{n(82885),n(86811),n(55539)}}]);