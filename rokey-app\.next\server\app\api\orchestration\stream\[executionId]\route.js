(()=>{var e={};e.id=6884,e.ids=[6884],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var s=r(34386),o=r(44999);async function n(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62854:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GET:()=>a,broadcastOrchestrationEvent:()=>c,cleanupOldStreams:()=>d,getActiveStreamCount:()=>p});var s=r(32190),o=r(2507);let n=new Map;async function a(e,{params:t}){let{executionId:r}=await t;if(!r)return s.NextResponse.json({error:"Execution ID is required"},{status:400});let a=(0,o.x)();try{let{data:e,error:t}=await a.from("orchestration_executions").select("*").eq("id",r).single();if(t||!e)return s.NextResponse.json({error:"Orchestration execution not found"},{status:404});let o=new TextEncoder,c=new ReadableStream({start(t){n.set(r,{controller:t,lastEventId:"",startTime:Date.now()});let s={id:crypto.randomUUID(),execution_id:r,type:"stream_connected",timestamp:new Date().toISOString(),data:{message:"\uD83C\uDFAC Connected to AI team orchestration stream",execution:{id:e.id,status:e.status,total_steps:e.total_steps,created_at:e.created_at}}},a=u(s);t.enqueue(o.encode(a)),"pending"!==e.status&&i(r,t,o)},cancel(){n.delete(r)}});return new Response(c,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control"}})}catch(e){return s.NextResponse.json({error:"Internal server error"},{status:500})}}async function i(e,t,r){let s=(0,o.x)();try{let{data:o,error:n}=await s.from("orchestration_steps").select("*").eq("execution_id",e).order("step_number",{ascending:!0});if(n)return;for(let s of o||[])for(let o of function(e,t){let r=[],s={execution_id:t,step_number:e.step_number,role_id:e.role_id,model_name:e.model_name};return r.push({id:crypto.randomUUID(),...s,type:"step_assigned",timestamp:e.created_at,data:{commentary:`📋 ${e.role_id} specialist assigned to step ${e.step_number}`,step:{number:e.step_number,role:e.role_id,model:e.model_name,prompt:e.prompt.substring(0,100)+"..."}}}),e.started_at&&r.push({id:crypto.randomUUID(),...s,type:"step_started",timestamp:e.started_at,data:{commentary:`🚀 ${e.role_id} is now working on this challenge...`,estimatedDuration:e.duration_ms||45e3}}),"in_progress"===e.status&&r.push({id:crypto.randomUUID(),...s,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:`⚡ ${e.role_id} is making excellent progress...`,progress:.6,partialOutput:e.response?e.response.substring(0,200)+"...":null}}),"completed"===e.status&&e.completed_at&&r.push({id:crypto.randomUUID(),...s,type:"step_completed",timestamp:e.completed_at,data:{commentary:`✅ Outstanding work from ${e.role_id}! Moving to next phase.`,output:e.response,duration:e.duration_ms,tokens:{input:e.tokens_in,output:e.tokens_out},cost:e.cost,quality:.9}}),"failed"===e.status&&r.push({id:crypto.randomUUID(),...s,type:"step_failed",timestamp:e.completed_at||new Date().toISOString(),data:{commentary:`❌ ${e.role_id} encountered an issue. Analyzing options...`,error:e.error_message,retryPlan:"Attempting automatic recovery"}}),r}(s,e)){let e=u(o);t.enqueue(r.encode(e)),await new Promise(e=>setTimeout(e,100))}}catch(e){}}function u(e){return`id: ${e.id}
event: ${e.type}
data: ${JSON.stringify(e)}

`}function c(e,t){let r=n.get(e);if(r)try{let e=new TextEncoder,s=u(t);r.controller.enqueue(e.encode(s)),r.lastEventId=t.id}catch(t){n.delete(e)}}function p(){return n.size}function d(e=18e5){let t=Date.now();for(let[r,s]of n.entries())if(t-s.startTime>e){try{s.controller.close()}catch(e){}n.delete(r)}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>i,serverHooks:()=>p,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var s=r(96559),o=r(48088),n=r(37719),a=r(62854);let i=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/orchestration/stream/[executionId]/route",pathname:"/api/orchestration/stream/[executionId]",filename:"route",bundlePath:"app/api/orchestration/stream/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\stream\\[executionId]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:p}=i;function d(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410],()=>r(99329));module.exports=s})();