(()=>{var e={};e.id=6884,e.ids=[6884],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{x:()=>a});var s=r(34386),o=r(44999);async function a(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},48332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>u});var o=r(96559),a=r(48088),i=r(37719),n=r(32190),c=r(2507),l=r(68811);async function u(e,{params:t}){let{executionId:r}=await t;if(!r)return n.NextResponse.json({error:"Execution ID is required"},{status:400});let s=await (0,c.x)();try{let{data:e,error:t}=await s.from("orchestration_executions").select("*").eq("id",r).single();if(t||!e)return n.NextResponse.json({error:"Orchestration execution not found"},{status:404});let o=new TextEncoder,a=new ReadableStream({start(t){(0,l.Wu)(r,t);let s={id:crypto.randomUUID(),execution_id:r,type:"orchestration_started",timestamp:new Date().toISOString(),data:{message:"\uD83C\uDFAC Connected to AI team orchestration stream",execution:{id:e.id,status:e.status,total_steps:e.total_steps,created_at:e.created_at}}},a=d(s);t.enqueue(o.encode(a)),"pending"!==e.status&&p(r,t,o)},cancel(){(0,l.ZZ)(r)}});return new Response(a,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control"}})}catch(e){return n.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,t,r){let s=await (0,c.x)();try{let{data:o,error:a}=await s.from("orchestration_steps").select("*").eq("execution_id",e).order("step_number",{ascending:!0});if(a)return;for(let s of o||[])for(let o of function(e,t){let r=[],s={execution_id:t,step_number:e.step_number,role_id:e.role_id,model_name:e.model_name};return r.push({id:crypto.randomUUID(),...s,type:"step_assigned",timestamp:e.created_at,data:{commentary:`📋 ${e.role_id} specialist assigned to step ${e.step_number}`,step:{number:e.step_number,role:e.role_id,model:e.model_name,prompt:e.prompt.substring(0,100)+"..."}}}),e.started_at&&r.push({id:crypto.randomUUID(),...s,type:"step_started",timestamp:e.started_at,data:{commentary:`🚀 ${e.role_id} is now working on this challenge...`,estimatedDuration:e.duration_ms||45e3}}),"in_progress"===e.status&&r.push({id:crypto.randomUUID(),...s,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:`⚡ ${e.role_id} is making excellent progress...`,progress:.6,partialOutput:e.response?e.response.substring(0,200)+"...":null}}),"completed"===e.status&&e.completed_at&&r.push({id:crypto.randomUUID(),...s,type:"step_completed",timestamp:e.completed_at,data:{commentary:`✅ Outstanding work from ${e.role_id}! Moving to next phase.`,output:e.response,duration:e.duration_ms,tokens:{input:e.tokens_in,output:e.tokens_out},cost:e.cost,quality:.9}}),"failed"===e.status&&r.push({id:crypto.randomUUID(),...s,type:"step_failed",timestamp:e.completed_at||new Date().toISOString(),data:{commentary:`❌ ${e.role_id} encountered an issue. Analyzing options...`,error:e.error_message,retryPlan:"Attempting automatic recovery"}}),r}(s,e)){let e=d(o);t.enqueue(r.encode(e)),await new Promise(e=>setTimeout(e,100))}}catch(e){}}function d(e){return`id: ${e.id}
event: ${e.type}
data: ${JSON.stringify(e)}

`}let m=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orchestration/stream/[executionId]/route",pathname:"/api/orchestration/stream/[executionId]",filename:"route",bundlePath:"app/api/orchestration/stream/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\stream\\[executionId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:g}=m;function x(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68811:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>a,ZZ:()=>i,Zi:()=>n,re:()=>c,tl:()=>o});let s=new Map;function o(e,t){let r=s.get(e);if(r)try{let e=new TextEncoder,s=`id: ${t.id}
event: ${t.type}
data: ${JSON.stringify(t)}

`;r.controller.enqueue(e.encode(s)),r.lastEventId=t.id}catch(t){s.delete(e)}}function a(e,t){s.set(e,{controller:t,lastEventId:"",startTime:Date.now()})}function i(e){s.delete(e)}async function n(e,t,r,s,a,i){let n={id:crypto.randomUUID(),execution_id:e,type:t,timestamp:new Date().toISOString(),data:r,step_number:s,role_id:a,model_name:i};o(e,n)}function c(e,t,r){let s={orchestration_started:["\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.","\uD83D\uDE80 Alright everyone, we've got an exciting challenge ahead. Let me coordinate our specialists.","\uD83C\uDFAF Time to bring together our expert AI team for optimal results!"],task_decomposed:["\uD83D\uDCCB I've analyzed the task and assembled this expert team. Let's begin the collaboration!","\uD83C\uDFAA Perfect! I've matched the right specialists to each part of this challenge.","⚡ Task breakdown complete. Our team is perfectly positioned for success."],moderator_assignment:[`🎯 @${t}, you're up! Please begin your specialized work on this task.`,`📋 @${t}, this is right in your wheelhouse. Take it away!`,`⚡ @${t}, I need your expertise here. Please work your magic.`],specialist_acknowledgment:[`✅ Understood! I'm ${t} and I'll handle this task with expertise. Starting work now...`,`🎯 Perfect! As the ${t} specialist, I'm excited to tackle this challenge.`,`⚡ Got it! ${t} here - I'll deliver excellent results for the team.`],specialist_message:[`🎉 Excellent! I've completed my part of the task. Here's what I've delivered:`,`✨ Perfect! My specialized work is complete. Take a look at the results:`,`🚀 Mission accomplished! Here's my contribution to the team effort:`],handoff_message:[`✨ Excellent work, @${t}! Quality looks great. Now passing to the next specialist...`,`👏 Outstanding execution, @${t}! Moving to the next phase...`,`🎉 Fantastic results, @${t}! The team collaboration continues...`],synthesis_started:["\uD83E\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...","\uD83C\uDFA8 Amazing collaboration! Time to weave all these brilliant outputs together...","✨ Outstanding work team! Let me combine everything into the perfect final result..."],synthesis_complete:["\uD83C\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!","\uD83C\uDFC6 Incredible teamwork! We've created something truly exceptional together.","\uD83C\uDF1F Perfect execution! This is what happens when AI specialists collaborate brilliantly."]}[e]||["\uD83E\uDD16 Processing..."],o=s[Math.floor(Math.random()*s.length)];return r&&"string"==typeof o?o.replace(/\{(\w+)\}/g,(e,t)=>r[t]||e):o}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,9398,3410],()=>r(48332));module.exports=s})();