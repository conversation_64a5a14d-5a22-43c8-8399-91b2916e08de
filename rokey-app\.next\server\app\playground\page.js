(()=>{var e={};e.id=3882,e.ids=[3882],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14689:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},14832:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19682:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"}))})},21590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\playground\\page.tsx","default")},23810:(e,t,r)=>{Promise.resolve().then(r.bind(r,64446))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32675:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687),s=r(43210),n=r(14689);let o=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))});function i({text:e,className:t="",size:r="sm",variant:i="default",title:l="Copy to clipboard"}){let[d,c]=(0,s.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(e),c(!0),setTimeout(()=>c(!1),2e3)}catch(r){let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{document.execCommand("copy"),c(!0),setTimeout(()=>c(!1),2e3)}catch(e){}document.body.removeChild(t)}},u={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,a.jsx)("button",{onClick:m,className:`
        ${{sm:"p-1.5",md:"p-2",lg:"p-2.5"}[r]}
        ${{default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[i]}
        rounded transition-all duration-200 cursor-pointer
        ${d?"text-green-600":""}
        ${t}
      `,title:d?"Copied!":l,children:d?(0,a.jsx)(n.A,{className:`${u[r]} stroke-2`}):(0,a.jsx)(o,{className:`${u[r]} stroke-2`})})}},33873:e=>{"use strict";e.exports=require("path")},45807:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(37413),s=r(47417);function n(){return(0,a.jsx)("div",{className:"h-screen flex bg-gray-50",children:(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-200 p-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-32 rounded"}),(0,a.jsx)(s.ConfigSelectorSkeleton,{})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-20 rounded"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-8 rounded-full"})]})]}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,a.jsx)("div",{className:"h-full flex justify-center",children:(0,a.jsx)("div",{className:"w-full max-w-4xl px-6",children:(0,a.jsx)(s.MessageSkeleton,{})})})}),(0,a.jsxs)("div",{className:"w-80 bg-white border-l border-gray-200 flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-gray-200",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-6 w-24 rounded mb-2"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-full rounded"})]}),(0,a.jsx)("div",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((e,t)=>(0,a.jsx)("div",{className:"p-3 rounded-xl border border-gray-100",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-2",children:[(0,a.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded"}),(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-16 rounded"}),(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-12 rounded"})]})]})},t))})})]})]}),(0,a.jsx)("div",{className:"bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-12 w-full rounded-xl"})})})]})})}},48935:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},54759:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["playground",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21590)),"C:\\RoKey App\\rokey-app\\src\\app\\playground\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,45807)),"C:\\RoKey App\\rokey-app\\src\\app\\playground\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\playground\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/playground/page",pathname:"/playground",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},58089:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64446:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>es});var a=r(60687),s=r(43210),n=r.n(s);let o=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),i=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),l=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),d=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),c=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),m=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))}),u=(0,s.lazy)(()=>r.e(317).then(r.bind(r,317))),g=()=>(0,a.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function h({content:e,className:t=""}){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(g,{}),children:(0,a.jsx)(u,{content:e,className:t})})}var x=r(32675),p=r(49579);let f=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}),b=new Map;function y({configId:e,onRetry:t,className:r="",disabled:n=!1}){let[o,i]=(0,s.useState)(!1),[l,d]=(0,s.useState)([]),[c,m]=(0,s.useState)(!1),[u,g]=(0,s.useState)(!1),h=(0,s.useRef)(null),x=(0,s.useCallback)(async(t=!0)=>{if(e){if(t){let t=b.get(e);if(t&&Date.now()-t.timestamp<3e5){d(t.keys),g(!0);return}}m(!0);try{let t=await fetch(`/api/keys?custom_config_id=${e}`);if(t.ok){let r=(await t.json()).filter(e=>"active"===e.status);b.set(e,{keys:r,timestamp:Date.now()}),d(r),g(!0)}}catch(e){}finally{m(!1)}}},[e]),y=e=>{i(!1),t(e)};return(0,a.jsxs)("div",{className:`relative ${r}`,ref:h,children:[(0,a.jsxs)("button",{onClick:()=>{o||0!==l.length||u||x(!0),i(!o)},disabled:n,className:`
          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer
          ${n?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20"}
        `,title:"Retry with different model",children:[(0,a.jsx)(p.A,{className:`w-4 h-4 stroke-2 ${c?"animate-spin":""}`}),(0,a.jsx)(f,{className:"w-3 h-3 stroke-2"})]}),o&&(0,a.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),x(!1)},disabled:c,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,a.jsx)(p.A,{className:`w-3 h-3 ${c?"animate-spin":""}`})})]}),(0,a.jsxs)("button",{onClick:()=>y(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{children:"Retry with same model"})]}),(l.length>0||c)&&(0,a.jsx)("div",{className:"border-t border-gray-100 my-1"}),c&&(0,a.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Loading models..."})]}),l.map(e=>(0,a.jsxs)("button",{onClick:()=>y(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:c,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsx)("span",{className:"font-medium",children:e.label}),(0,a.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!c&&0===l.length&&u&&(0,a.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),l.length>0&&!c&&(0,a.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[l.length," model",1!==l.length?"s":""," available"]}),(()=>{let t=b.get(e);return t&&Date.now()-t.timestamp<3e5?(0,a.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}var w=r(76180),v=r.n(w),j=r(14832);let k=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var N=r(19682),C=r(48935),S=r(62392);let T=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),E=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var A=r(71178),_=r(64364),M=r(50942);let L={initializing:{icon:j.A,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:k,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:p.A,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:N.A,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:C.A,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:S.A,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:T,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:E,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:A.A,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:_.A,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:M.A,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function $({currentStage:e,isStreaming:t=!1,className:r="",onStageChange:n,orchestrationStatus:o}){let[i,l]=(0,s.useState)(e),[d,c]=(0,s.useState)(!1),m=L[i],u=m.icon;return(0,a.jsxs)("div",{className:`jsx-f56d70faa8a01b64 flex justify-start ${r}`,children:[(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,a.jsx)("div",{style:{animation:d?"spin 0.6s linear infinite":"spin 1.2s linear infinite",borderImage:`conic-gradient(from 0deg, transparent 0%, ${m.iconColor.replace("text-","")} 25%, transparent 50%, ${m.iconColor.replace("text-","")} 75%, transparent 100%) 1`,filter:"drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))"},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin"}),(0,a.jsx)("div",{style:{animation:d?"spin 0.8s linear infinite reverse":"spin 1.6s linear infinite reverse",borderImage:`conic-gradient(from 180deg, transparent 0%, ${m.iconColor.replace("text-","")} 30%, transparent 60%, ${m.iconColor.replace("text-","")} 90%, transparent 100%) 1`,opacity:.8},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin"}),(0,a.jsx)("div",{style:{borderColor:m.iconColor.replace("text-",""),opacity:.6,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse"}),(0,a.jsx)("div",{style:{boxShadow:`0 0 12px ${m.iconColor.replace("text-","")}40, 0 0 24px ${m.iconColor.replace("text-","")}20`},className:`jsx-f56d70faa8a01b64 relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ${m.bgColor} border-2 ${m.borderColor} shadow-lg backdrop-blur-sm`,children:(0,a.jsx)(u,{className:`jsx-f56d70faa8a01b64 w-3.5 h-3.5 transition-all duration-500 ${m.iconColor} ${d?"scale-125 rotate-12":"scale-100"} drop-shadow-lg`})})]}),(0,a.jsxs)("div",{className:`jsx-f56d70faa8a01b64 max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ${m.bgColor} ${m.borderColor} border ${m.glowColor} shadow-sm backdrop-blur-sm`,children:[(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,a.jsx)("span",{className:`jsx-f56d70faa8a01b64 text-xs font-semibold transition-colors duration-500 ${m.iconColor} tracking-wide`,children:o||m.text}),t&&"typing"===i&&!o&&(0,a.jsx)("span",{className:`jsx-f56d70faa8a01b64 ml-1.5 text-[10px] opacity-80 ${m.iconColor} font-medium`,children:"• Live"}),o&&(0,a.jsx)("span",{className:`jsx-f56d70faa8a01b64 ml-1.5 text-[10px] opacity-80 ${m.iconColor} font-medium`,children:"• Orchestrating"})]})}),("generating"===i||"typing"===i)&&(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,a.jsx)("div",{style:{width:"typing"===i?"100%":"60%",animation:"typing"===i?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:`jsx-f56d70faa8a01b64 h-full rounded-full transition-all duration-1000 bg-gradient-to-r ${m.gradientFrom} ${m.gradientTo} relative overflow-hidden`,children:(0,a.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,a.jsx)(v(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}var R=r(68589),P=r(7610),I=r(58089);let O=({message:e})=>{var t,r;let s=e=>{switch(e){case"assignment":return(0,a.jsx)(P.A,{className:"w-3 h-3 text-blue-500"});case"completion":return(0,a.jsx)(I.A,{className:"w-3 h-3 text-green-500"});case"handoff":return(0,a.jsx)(P.A,{className:"w-3 h-3 text-purple-500"});default:return null}},o="moderator"===e.sender,i=(e=>{if(!e)return"from-blue-500 to-blue-600";let t=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(e.roleId);return(0,a.jsx)("div",{className:"flex justify-start mb-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,a.jsx)("div",{className:`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${i} flex items-center justify-center text-white shadow-sm`,children:(t=e.sender,e.roleId,"moderator"===t?(0,a.jsx)(_.A,{className:"w-4 h-4"}):(0,a.jsx)(R.A,{className:"w-4 h-4"}))}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:`text-sm font-semibold ${o?"text-blue-700":"text-gray-700"}`,children:e.senderName}),s(e.type)&&(0,a.jsx)("div",{className:"flex items-center",children:s(e.type)}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,a.jsx)("div",{className:`inline-block px-4 py-3 rounded-2xl shadow-sm ${o?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"} ${"completion"===e.type?"border-green-200 bg-green-50":"assignment"===e.type?"border-blue-200 bg-blue-50":"handoff"===e.type?"border-purple-200 bg-purple-50":""}`,children:(0,a.jsx)("div",{className:`text-sm leading-relaxed ${o?"text-blue-900":"text-gray-800"} ${"completion"===e.type?"text-green-900":"assignment"===e.type?"text-blue-900":"handoff"===e.type?"text-purple-900":""}`,children:e.content.split("\n").map((t,r)=>(0,a.jsxs)(n().Fragment,{children:[t,r<e.content.split("\n").length-1&&(0,a.jsx)("br",{})]},r))})}),"message"!==e.type&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${"assignment"===e.type?"bg-blue-100 text-blue-800":"completion"===e.type?"bg-green-100 text-green-800":"handoff"===e.type?"bg-purple-100 text-purple-800":"clarification"===e.type?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:["assignment"===e.type&&"\uD83D\uDCCB Task Assignment","completion"===e.type&&"✅ Task Complete","handoff"===e.type&&"\uD83D\uDD04 Handoff","clarification"===e.type&&"❓ Clarification"]})})]})]})})},z=({senderName:e,roleId:t})=>{let r=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let t=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(t),s=!t||"moderator"===t;return(0,a.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,a.jsx)("div",{className:`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${r} flex items-center justify-center text-white shadow-sm animate-pulse`,children:(e=>e&&"moderator"!==e?(0,a.jsx)(R.A,{className:"w-4 h-4"}):(0,a.jsx)(_.A,{className:"w-4 h-4"}))(t)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:`text-sm font-semibold ${s?"text-blue-700":"text-gray-700"}`,children:e}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let t=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(e)})]}),(0,a.jsx)("div",{className:`inline-block px-4 py-3 rounded-2xl shadow-sm ${s?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"}`,children:(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})},D=({executionId:e,events:t,isConnected:r,error:n,isComplete:o})=>{let[i,l]=(0,s.useState)([]),[d,c]=(0,s.useState)(new Set),m=(0,s.useRef)(null);return((0,s.useEffect)(()=>{m.current?.scrollIntoView({behavior:"smooth"})},[i]),(0,s.useEffect)(()=>{let r=[],a=new Set;t.forEach((t,s)=>{let n=new Date(t.timestamp||Date.now()),o=`${e}-${s}`;switch(t.type){case"orchestration_started":r.push({id:o,sender:"moderator",senderName:"Moderator",content:"\uD83C\uDFAC Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.",timestamp:n,type:"message"});break;case"task_decomposed":let i=(t.data?.steps||[]).map(e=>`🤖 @${e.roleId} - ${e.modelName||"AI Specialist"}`).join("\n");r.push({id:o,sender:"moderator",senderName:"Moderator",content:`📋 I've analyzed the task and assembled this expert team:

${i}

Let's begin the collaboration!`,timestamp:n,type:"assignment"});break;case"step_assigned":r.push({id:o,sender:"moderator",senderName:"Moderator",roleId:t.role_id,content:`🎯 @${t.role_id}, you're up! ${t.data?.commentary||"Please begin your specialized work on this task."}`,timestamp:n,type:"assignment"});break;case"moderator_assignment":r.push({id:o,sender:"moderator",senderName:"Moderator",roleId:t.role_id,content:t.data?.message||`🎯 @${t.role_id}, you're up! Please begin your specialized work on this task.`,timestamp:n,type:"assignment"});break;case"specialist_acknowledgment":r.push({id:o,sender:"specialist",senderName:t.role_id||"Specialist",roleId:t.role_id,content:t.data?.message||`✅ Understood! I'm ${t.role_id} and I'll handle this task with expertise. Starting work now...`,timestamp:n,type:"message"});break;case"step_started":case"step_progress":t.role_id&&a.add(t.role_id);break;case"specialist_message":r.push({id:o,sender:"specialist",senderName:t.role_id||"Specialist",roleId:t.role_id,content:`${t.data?.message||"\uD83C\uDF89 Perfect! I've completed my part of the task. Here's what I've delivered:"}

${t.data?.output||"Task completed successfully!"}`,timestamp:n,type:"completion"});break;case"step_completed":t.role_id&&a.delete(t.role_id);break;case"handoff_message":r.push({id:o,sender:"moderator",senderName:"Moderator",content:t.data?.message||`✨ Excellent work, @${t.data?.fromRole}! Quality looks great. Now passing to @${t.data?.toRole}...`,timestamp:n,type:"handoff"});break;case"synthesis_started":r.push({id:o,sender:"moderator",senderName:"Moderator",content:t.data?.message||`🧩 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...`,timestamp:n,type:"message"}),a.add("moderator");break;case"synthesis_complete":a.delete("moderator"),r.push({id:o,sender:"moderator",senderName:"Moderator",content:t.data?.message||`🎊 Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!`,timestamp:n,type:"completion"})}}),l(r),c(a)},[t,e]),n)?(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Connection Error"}),(0,a.jsx)("p",{className:"text-gray-600",children:n})]})}):(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:`px-4 py-2 text-xs font-medium ${r?"bg-green-50 text-green-700 border-b border-green-100":"bg-yellow-50 text-yellow-700 border-b border-yellow-100"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${r?"bg-green-500":"bg-yellow-500"}`}),(0,a.jsx)("span",{children:r?"Connected to AI Team":"Connecting..."})]})}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[0===i.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-blue-600 animate-pulse",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,a.jsx)("p",{className:"text-gray-500",children:"Waiting for AI team to start collaboration..."})]}),i.map(e=>(0,a.jsx)(O,{message:e},e.id)),Array.from(d).map(e=>(0,a.jsx)(z,{senderName:e,roleId:"moderator"!==e?e:void 0},e)),(0,a.jsx)("div",{ref:m})]})]})},H=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))}),B=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))}),F=({executionId:e,onComplete:t,onError:r,onCanvasStateChange:n,forceMaximize:o=!1})=>{let[i,l]=(0,s.useState)(!0),[d,c]=(0,s.useState)(!1),[m,u]=(0,s.useState)(!1),[g,h]=(0,s.useState)(""),{events:x,isConnected:p,error:f}=function(e,t){let[r,a]=(0,s.useState)([]),[n,o]=(0,s.useState)(!1),[i,l]=(0,s.useState)(null),[d,c]=(0,s.useState)(null),[m,u]=(0,s.useState)(""),g=(0,s.useRef)(null),h=(0,s.useRef)(null);(0,s.useRef)(null);let x=(0,s.useRef)(0),p=(0,s.useRef)(""),f=(0,s.useRef)(""),b=(0,s.useCallback)(()=>{g.current&&(g.current.close(),g.current=null),h.current&&(clearTimeout(h.current),h.current=null),o(!1)},[]),y=(0,s.useCallback)(()=>{if(!e)return void l("No execution ID or direct stream URL provided");let r=t||(e?`/api/orchestration/stream/${e}`:"");if(!r)return void l("No valid stream URL could be determined");if(f.current!==r||!n){b(),p.current=e||"",f.current=r;try{let e=new EventSource(r);g.current=e,e.onopen=()=>{o(!0),l(null),x.current=0},e.onmessage=e=>{try{let t=JSON.parse(e.data);a(e=>[...e,t]),c(t),l(null)}catch(e){l("Error parsing stream data")}},e.addEventListener("orchestration_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_started",e=>{JSON.parse(e.data)}),e.addEventListener("step_progress",e=>{JSON.parse(e.data)}),e.addEventListener("step_completed",e=>{JSON.parse(e.data)}),e.addEventListener("synthesis_started",e=>{JSON.parse(e.data)}),e.addEventListener("orchestration_completed",e=>{JSON.parse(e.data)}),e.onerror=e=>{if(o(!1),x.current<5){let e=1e3*Math.pow(2,x.current);x.current++,l(`Connection lost. Reconnecting in ${e/1e3}s... (attempt ${x.current}/5)`),h.current=setTimeout(()=>{y()},e)}else l("Connection failed after multiple attempts. Please refresh the page.")}}catch(e){l("Failed to establish connection"),o(!1)}}},[e,b]);return{events:r,isConnected:n,error:i,lastEvent:d,reconnect:(0,s.useCallback)(()=>{x.current=0,y()},[y]),disconnect:b}}(e);(0,s.useEffect)(()=>{let e=x.find(e=>"synthesis_complete"===e.type);if(e&&!m){u(!0);let r=e.data?.result||"Orchestration completed successfully";h(r),t&&t(r)}},[x,m,t]),(0,s.useEffect)(()=>{f&&r&&r(f)},[f,r]);let b=()=>{c(!1),l(!0),n?.(!0,!1)};return((0,s.useEffect)(()=>{n?.(i,d)},[i,d,n]),(0,s.useEffect)(()=>{o&&d&&b()},[o,d]),d||!i)?null:(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:`fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out border-l border-orange-500/20 ${i&&!d?"translate-x-0":"translate-x-full"}`,children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none"}),(0,a.jsx)("div",{className:"absolute left-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-orange-500 to-transparent animate-pulse"}),(0,a.jsxs)("div",{className:"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm",children:[(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,a.jsx)(H,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"font-bold text-white text-lg tracking-wide",children:"AI Team Collaboration"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${m?"bg-green-400":"bg-orange-400"} animate-pulse`}),(0,a.jsx)("p",{className:"text-sm text-gray-300 font-medium",children:m?"Mission Complete":"Team Active"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full",children:[(0,a.jsx)(_.A,{className:"w-4 h-4 text-orange-400"}),(0,a.jsx)("span",{className:"text-xs text-orange-300 font-medium",children:"LIVE"})]}),(0,a.jsxs)("button",{onClick:()=>{c(!0),n?.(!1,!0)},className:"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30","aria-label":"Minimize canvas",children:[(0,a.jsx)(B,{className:"w-5 h-5 transition-transform group-hover:scale-110"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10"})]})]})]}),(0,a.jsxs)("div",{className:"flex-1 h-full overflow-hidden relative",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]"})]}),(0,a.jsx)(D,{executionId:e,events:x,isConnected:p,error:f,isComplete:m})]})]})})},W=({orchestrationComplete:e,onMaximize:t,isCanvasOpen:r,isCanvasMinimized:s})=>(0,a.jsxs)("div",{className:`flex justify-start group mb-16 mt-8 ${r&&!s?"-ml-96":""} ${r&&!s?"ml-8":""}`,children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsx)("div",{className:`${r&&!s?"max-w-[80%]":"max-w-[65%]"} relative`,children:(0,a.jsx)("div",{onClick:t,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px] ring-2 ring-blue-300/60 hover:ring-blue-300/80 shadow-blue-500/20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(H,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"AI Team Collaboration"}),(0,a.jsx)("p",{className:"text-xs opacity-90",children:e?"Completed - Click to view results":"Multi-Role Orchestration in progress"})]}),(0,a.jsxs)("div",{className:"flex-shrink-0",children:[!e&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),e&&(0,a.jsx)(_.A,{className:"w-5 h-5"})]})]})})})]});var q=r(50549),U=r(48427);let J={initializing:50,analyzing:150,routing:200,complexity_analysis:250,role_classification:300,preparing:150,connecting:200,generating:400,typing:0,finalizing:100,complete:0};class V{static getInstance(){return V.instance||(V.instance=new V),V.instance}trackParallelFlow(e){let t=`${e.provider}_${e.model}_parallel`;this.parallelMetrics||(this.parallelMetrics=new Map),this.parallelMetrics.has(t)||this.parallelMetrics.set(t,[]);let r=this.parallelMetrics.get(t);r.push({...e,timestamp:Date.now()}),r.length>this.maxSamples&&r.shift(),e.firstTokenTime}trackMessagingFlow(e){let t=`${e.provider}_${e.model}`;this.metrics.has(t)||this.metrics.set(t,[]);let r=this.metrics.get(t);r.push({timestamp:Date.now(),...e}),r.length>this.maxSamples&&r.shift(),this.logPerformanceInsights(e)}getStats(e,t){let r=`${e}_${t}`,a=this.metrics.get(r);if(!a||0===a.length)return null;let s=a.filter(e=>e.success);if(0===s.length)return null;let n=s.map(e=>e.timings.total),o=s.map(e=>e.timings.llmApiCall),i=s.map(e=>e.messageLength);return{provider:e,model:t,sampleCount:s.length,averageTotal:this.calculateAverage(n),averageLLM:this.calculateAverage(o),medianTotal:this.calculateMedian(n),medianLLM:this.calculateMedian(o),p95Total:this.calculatePercentile(n,95),p95LLM:this.calculatePercentile(o,95),minTotal:Math.min(...n),maxTotal:Math.max(...n),averageMessageLength:this.calculateAverage(i),streamingUsage:s.filter(e=>e.isStreaming).length/s.length,errorRate:(a.length-s.length)/a.length,recentTrend:this.calculateTrend(n.slice(-10))}}getSummary(){let e=new Set,t=[];for(let r of this.metrics.keys()){let[a,s]=r.split("_");e.add(a);let n=this.getStats(a,s);n&&t.push(n)}if(0===t.length)return{totalProviders:0,totalModels:0,overallAverageTime:0,fastestProvider:null,slowestProvider:null,recommendations:["No messaging data available yet"]};let r=this.calculateAverage(t.map(e=>e.averageTotal)),a=[...t].sort((e,t)=>e.averageTotal-t.averageTotal);return{totalProviders:e.size,totalModels:t.length,overallAverageTime:r,fastestProvider:a[0],slowestProvider:a[a.length-1],recommendations:this.generateRecommendations(t)}}generateRecommendations(e){let t=[],r=e.filter(e=>e.averageTotal>5e3);r.length>0&&t.push(`Consider switching from slow providers: ${r.map(e=>e.provider).join(", ")}`),e.filter(e=>e.streamingUsage<.5).length>0&&t.push("Enable streaming for better perceived performance");let a=e.filter(e=>e.errorRate>.1);a.length>0&&t.push(`High error rates detected for: ${a.map(e=>e.provider).join(", ")}`);let s=e.filter(e=>e.averageTotal<=2e3);return 0===s.length?t.push("No providers meeting 2s target - consider optimizing or switching providers"):t.push(`Fast providers (≤2s): ${s.map(e=>e.provider).join(", ")}`),t.length>0?t:["Performance looks good!"]}logPerformanceInsights(e){let{provider:t,model:r,timings:a,isStreaming:s,streamingMetrics:n}=e;s&&a.timeToFirstToken&&(a.timeToFirstToken<500||a.timeToFirstToken<1e3||a.timeToFirstToken,n&&n.averageTokenLatency),a.total,a.total,a.total,a.llmApiCall,a.llmApiCall,a.total,a.backendProcessing&&a.frontendProcessing&&(a.backendProcessing,a.total,a.frontendProcessing,a.total),s||a.total}calculateAverage(e){return e.reduce((e,t)=>e+t,0)/e.length}calculateMedian(e){let t=[...e].sort((e,t)=>e-t),r=Math.floor(t.length/2);return t.length%2==0?(t[r-1]+t[r])/2:t[r]}calculatePercentile(e,t){let r=[...e].sort((e,t)=>e-t),a=Math.ceil(t/100*r.length)-1;return r[Math.max(0,a)]}calculateTrend(e){if(e.length<5)return"stable";let t=e.slice(0,Math.floor(e.length/2)),r=e.slice(Math.floor(e.length/2)),a=this.calculateAverage(t),s=(this.calculateAverage(r)-a)/a*100;return s<-10?"improving":s>10?"degrading":"stable"}exportData(){let e={};for(let[t,r]of this.metrics.entries())e[t]=[...r];return e}clear(){this.metrics.clear()}constructor(){this.metrics=new Map,this.maxSamples=50,this.parallelMetrics=new Map}}class Y{static getInstance(){return Y.instance||(Y.instance=new Y),Y.instance}startRequest(e,t,r){this.timingData.set(e,{requestStart:performance.now(),tokenCount:0,provider:t,model:r})}markFirstToken(e){let t=this.timingData.get(e);if(!t)return null;let r=performance.now()-t.requestStart;return t.firstTokenReceived=performance.now(),r}trackToken(e){let t=this.timingData.get(e);t&&t.tokenCount++}completeStream(e){let t=this.timingData.get(e);if(!t||!t.firstTokenReceived)return null;let r=performance.now();t.streamComplete=r;let a=t.firstTokenReceived-t.requestStart,s=r-t.requestStart,n=r-t.firstTokenReceived,o=t.tokenCount>1?n/(t.tokenCount-1):0,i={timeToFirstToken:a,totalStreamTime:s,totalTokens:t.tokenCount,averageTokenLatency:o};return this.timingData.delete(e),i}getStatus(e){let t=this.timingData.get(e);return t?t.firstTokenReceived?t.streamComplete?"Complete":"Streaming in progress":"Waiting for first token":"Not tracked"}clear(){this.timingData.clear()}constructor(){this.timingData=new Map}}let Z=V.getInstance(),K=Y.getInstance();function G(){let e=Z.getSummary();e.fastestProvider,e.slowestProvider,e.recommendations.forEach(e=>console.log(`   • ${e}`))}function X(){K.timingData?.size}function Q(){}function ee(){let e=setInterval(()=>{Z.getSummary().totalProviders},3e4);globalThis.__performanceMonitoringInterval=e}function et(){let e=globalThis.__performanceMonitoringInterval;e&&(clearInterval(e),delete globalThis.__performanceMonitoringInterval)}function er(){let e=Z.getSummary();0!==e.totalProviders&&(e.overallAverageTime<2e3||e.overallAverageTime<5e3||e.overallAverageTime,e.fastestProvider,e.slowestProvider)}"undefined"!=typeof globalThis&&(globalThis.logComprehensivePerformanceReport=G,globalThis.logFirstTokenReport=X,globalThis.logGoogleStreamingDebug=Q,globalThis.quickPerformanceCheck=er,globalThis.startPerformanceMonitoring=ee,globalThis.stopPerformanceMonitoring=et,globalThis.performanceLogs={comprehensive:G,firstToken:X,googleDebug:Q,quick:er,startMonitoring:ee,stopMonitoring:et});let ea=n().memo(({chat:e,currentConversation:t,onLoadChat:r,onDeleteChat:s})=>{let n=t?.id===e.id;return(0,a.jsxs)("div",{className:`relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 ${n?"bg-orange-50 border border-orange-200":""}`,children:[(0,a.jsx)("button",{onClick:()=>r(e),className:"w-full text-left",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate mb-1",children:e.title}),e.last_message_preview&&(0,a.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:e.last_message_preview}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,a.jsxs)("span",{children:[e.message_count," messages"]}),(0,a.jsx)("span",{children:new Date(e.updated_at).toLocaleDateString()})]})]})})}),(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),s(e.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200",title:"Delete conversation",children:(0,a.jsx)(o,{className:"w-4 h-4"})})]})});function es(){let{isCollapsed:e,isHovered:t,setHoverDisabled:r}=(0,q.c)(),n=!e||t?"256px":"64px",[o,u]=(0,s.useState)([]),[g,p]=(0,s.useState)(""),[f,b]=(0,s.useState)(!0),[w,v]=(0,s.useState)(""),[j,k]=(0,s.useState)([]),[N,C]=(0,s.useState)(!1),[S,T]=(0,s.useState)(null),[E,A]=(0,s.useState)(!0),[_,M]=(0,s.useState)(!1),[L,R]=(0,s.useState)([]),[P,I]=(0,s.useState)([]),O=(0,s.useRef)(null),z=(0,s.useRef)(null),D=(0,s.useRef)(null),[H,B]=(0,s.useState)(!1),[V,Y]=(0,s.useState)(null),[Z,K]=(0,s.useState)(null),[G,X]=(0,s.useState)(""),[Q,ee]=(0,s.useState)(!1),[et,er]=(0,s.useState)(null),[es,en]=(0,s.useState)(!1),[eo,ei]=(0,s.useState)(!1),[el,ed]=(0,s.useState)(!1),[ec,em]=(0,s.useState)(!1),[eu,eg]=(0,s.useState)(!1),eh=function(e={}){let t=function(e={}){let{enableAutoProgression:t=!0,stageDurations:r={},onStageChange:a}=e,[n,o]=(0,s.useState)("initializing"),[i,l]=(0,s.useState)(!1),[d,c]=(0,s.useState)([]),m=(0,s.useRef)(0),u=(0,s.useRef)([]);({...J,...r});let g=(0,s.useCallback)(()=>{u.current.forEach(e=>clearTimeout(e)),u.current=[]},[]),h=(0,s.useCallback)((e,t=!0)=>{let r=Date.now();if("connecting"===e){o(e),c(t=>[...t,{stage:e,timestamp:r}]),a?.(e,r);let t=setTimeout(()=>{o("routing"),c(e=>[...e,{stage:"routing",timestamp:Date.now()}]),a?.("routing",Date.now())},2e3);u.current.push(t);return}t&&g(),o(e),c(t=>[...t,{stage:e,timestamp:r}]),a?.(e,r)},[a,g]),x=(0,s.useCallback)(()=>{let e;l(!0),m.current=Date.now(),c([{stage:"initializing",timestamp:Date.now()}]),o("initializing");let t=(e,t=30)=>{let r=(Math.random()-.5)*2*(t/100*e);return Math.max(200,Math.round(e+r))},r=setTimeout(()=>{h("analyzing",!1)},e=0+t(900,35)),a=setTimeout(()=>{h("complexity_analysis",!1)},e+=t(1200,40)),s=setTimeout(()=>{h("role_classification",!1)},e+=t(1500,35)),n=setTimeout(()=>{h("preparing",!1)},e+=t(1e3,40)),i=setTimeout(()=>{h("connecting",!1)},e+=t(1200,35)),d=setTimeout(()=>{h("routing",!1)},e+=t(1500,40)),g=setTimeout(()=>{h("generating",!1)},e+=t(1200,35));u.current.push(r,a,s,n,i,d,g)},[a,h]),p=(0,s.useCallback)(()=>{g();let e=Date.now();o("typing"),c(t=>[...t,{stage:"typing",timestamp:e}]),a?.("typing",e)},[g,a]),f=(0,s.useCallback)(()=>{g(),h("complete"),l(!1)},[g,h]),b=(0,s.useCallback)(()=>{g();let e=Date.now();o("generating"),c(t=>[...t,{stage:"generating",timestamp:e}]),a?.("generating",e)},[g,a]),y=(0,s.useCallback)(e=>{},[]);return{currentStage:n,isActive:i,stageHistory:d,startProcessing:x,updateStage:h,markStreaming:p,markComplete:f,markOrchestrationStarted:b,updateOrchestrationStatus:y,reset:(0,s.useCallback)(()=>{g(),o("initializing"),l(!1),c([]),m.current=0},[g]),getProcessingDuration:(0,s.useCallback)(()=>0===m.current?0:Date.now()-m.current,[])}}(e),[r,a]=(0,s.useState)(new Set),n=(0,s.useCallback)(e=>{e.get("x-rokey-role-used"),e.get("x-rokey-routing-strategy"),e.get("x-rokey-complexity-level"),e.get("x-rokey-api-key-provider"),t.updateStage("generating")},[t]),o=(0,s.useCallback)(e=>{e.includes("[Complexity Classification]")&&!r.has("complexity")&&(t.updateStage("complexity_analysis"),a(e=>new Set([...e,"complexity"]))),e.includes("[Intelligent Role Strategy]")&&!r.has("role")&&(t.updateStage("role_classification"),a(e=>new Set([...e,"role"]))),e.includes("FIRST TOKEN:")&&!r.has("streaming")&&(t.markStreaming(),a(e=>new Set([...e,"streaming"])))},[t,r]),i=(0,s.useCallback)(()=>{t.reset(),a(new Set)},[t]);return{...t,analyzeResponseHeaders:n,analyzeStreamChunk:o,reset:i,detectedStages:Array.from(r)}}({enableAutoProgression:!0,onStageChange:(e,t)=>{}}),[ex,ep]=(0,s.useState)(""),ef=(e,t)=>{let r="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))r="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))r="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))r="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);r=t?`${t[1]} Specialist working`:"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?r="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(r="Analyzing and processing with specialized expertise");r&&r!==ex&&(ep(r),t.updateOrchestrationStatus(r))},eb=async()=>{if(g&&V){C(!0),ep("Continuing synthesis automatically..."),eh.startProcessing();try{let e={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};k(t=>[...t,e]),await eP(V.id,e);let t={custom_api_config_id:g,messages:[...j.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:E},r=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(t),cache:"no-store"});if(r.ok){let e,t=await r.text();try{e=JSON.parse(t)}catch{e=null}if(e?.error==="synthesis_complete"){k(e=>e.slice(0,-1)),C(!1),ep(""),eh.markComplete(),v("continue"),setTimeout(()=>{eU()},100);return}let a=new Response(t,{status:r.status,statusText:r.statusText,headers:r.headers});if(E&&a.body){let e=a.body.getReader(),t=new TextDecoder,r=Date.now().toString()+"-assistant-continue",s={id:r,role:"assistant",content:[{type:"text",text:""}]};k(e=>[...e,s]);let n="",o=!1,i=null,l=a.headers.get("X-Synthesis-Progress"),d=a.headers.get("X-Synthesis-Complete"),c=null!==l;for(c?(eh.markStreaming(),ep("")):(eh.markOrchestrationStarted(),ep("Continuing synthesis..."),i=setTimeout(()=>{o||(eh.markStreaming(),ep(""))},800));;){let{done:a,value:l}=await e.read();if(a)break;for(let e of t.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if(e.choices&&e.choices[0]?.delta?.content){let t=e.choices[0].delta.content;n+=t,!c&&!o&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))?(o=!0,i&&(clearTimeout(i),i=null),ef(t,eh)):!c&&o&&ef(t,eh);let a=s.content[0];a.text=n,k(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(i&&clearTimeout(i),n){let e={...s,content:[{type:"text",text:n}]};c&&"true"!==d&&n.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await eP(V.id,e),setTimeout(()=>{eb()},1e3)):await eP(V.id,e)}}}else throw Error(`Auto-continuation failed: ${r.status}`)}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:`Auto-continuation failed: ${t instanceof Error?t.message:"Unknown error"}`}]};k(t=>[...t,e])}finally{C(!1),ep(""),eh.markComplete()}}},{chatHistory:ey,isLoading:ew,isStale:ev,error:ej,refetch:ek,prefetch:eN,invalidateCache:eC}=(0,U.mx)({configId:g,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eS}=(0,U.l2)(),eT=e=>new Promise((t,r)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>t(a.result),a.onerror=e=>r(e)}),eE=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let r=L.length,a=t.slice(0,10-r);a.length<t.length&&T(`You can only upload up to 10 images. ${t.length-a.length} images were not added.`);try{let e=[];for(let t of a){let r=await eT(t);e.push(r)}R(e=>[...e,...a]),I(t=>[...t,...e])}catch(e){T("Failed to process one or more images. Please try again.")}O.current&&(O.current.value="")},eA=e=>{void 0!==e?(R(t=>t.filter((t,r)=>r!==e)),I(t=>t.filter((t,r)=>r!==e))):(R([]),I([])),O.current&&(O.current.value="")},e_=(e=!1)=>{D.current&&D.current.scrollTo({top:D.current.scrollHeight,behavior:e?"smooth":"auto"})},eM=async(e,t=!1)=>{t||ee(!0);try{let r=t?j.length:0,a=Date.now(),s=await fetch(`/api/chat/messages?conversation_id=${e.id}&limit=50&offset=${r}&latest=${!t}&_cb=${a}`,{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!s.ok)throw Error("Failed to load conversation messages");let n=(await s.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""})}));t?k(e=>[...n,...e]):(k(n),V&&V.id===e.id||Y(e)),T(null)}catch(e){T(`Failed to load conversation: ${e.message}`)}finally{t||ee(!1)}},eL=async()=>{if(!g||0===j.length)return null;try{let e=V?.id;if(!e){let t=j[0],r="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(r=e.text.slice(0,50)+(e.text.length>50?"...":""))}let a={custom_api_config_id:g,title:r},s=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to create conversation");let n=await s.json();e=n.id,Y(n)}for(let t of j){if(t.id.includes("-")&&t.id.length>20)continue;let r={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})}return V||ek(!0),e}catch(e){return T(`Failed to save conversation: ${e.message}`),null}},e$=async e=>{try{if(!(await fetch(`/api/chat/conversations?id=${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete conversation");V?.id===e&&(Y(null),k([])),ek(!0)}catch(e){T(`Failed to delete conversation: ${e.message}`)}},eR=async e=>{if(!g)return null;try{let t="New Chat";if(e.content.length>0){let r=e.content.find(e=>"text"===e.type);r&&r.text&&(t=r.text.slice(0,50)+(r.text.length>50?"...":""))}let r={custom_api_config_id:g,title:t},a=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok)throw Error("Failed to create conversation");let s=await a.json();return Y(s),s.id}catch(e){return T(`Failed to create conversation: ${e.message}`),null}},eP=async(e,t)=>{try{let r={conversation_id:e,role:t.role,content:t.content},a=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok)throw Error("Failed to save message");return await a.json()}catch(e){}},eI=e=>{v(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},eO=async()=>{j.length>0&&await eL(),k([]),Y(null),v(""),T(null),eA(),eh.reset()},ez=async e=>{if(e===g)return;j.length>0&&await eO(),p(e);let t=o.find(t=>t.id===e);t&&t.name},eD=async e=>{Y(e),k([]),v(""),T(null),eA();let t=(async()=>{if(j.length>0&&!V)try{await eL()}catch(e){}})();try{await eM(e)}catch(e){T(`Failed to load conversation: ${e.message}`)}await t},eH=(e,t)=>{K(e),X(t)},eB=()=>{K(null),X("")},eF=async()=>{if(!Z||!G.trim()||!g)return;let e=j.findIndex(e=>e.id===Z);if(-1===e)return;let t=[...j];t[e]={...t[e],content:[{type:"text",text:G.trim()}]};let r=t.slice(0,e+1);if(k(r),K(null),X(""),V)try{if(j.slice(e+1).length>0){let t=j[e],r=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:V.id,after_timestamp:r})});a.ok&&await a.json()}let t=r[e],a=await fetch("/api/chat/messages/update-by-timestamp",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:V.id,timestamp:parseInt(t.id),content:t.content})});a.ok?await a.json():await eP(V.id,t),ek(!0)}catch(e){T(`Failed to update conversation: ${e.message}`)}await eW(r)},eW=async e=>{if(!g||0===e.length)return;C(!0),T(null),eh.startProcessing();let t=e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let r=e.content[0];t=r&&"text"===r.type?r.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}});try{eh.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify({custom_api_config_id:g,messages:t,stream:E}),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||`API Error: ${e.statusText} (Status: ${e.status})`)}if(eh.analyzeResponseHeaders(e.headers),setTimeout(()=>{E&&eh.markStreaming()},400),E&&e.body){let t=e.body.getReader(),r=new TextDecoder,a=Date.now().toString()+"-assistant",s={id:a,role:"assistant",content:[{type:"text",text:""}]};k(e=>[...e,s]);let n="",o=!1,i=null;for(i=setTimeout(()=>{o||eh.markStreaming()},400);;){let{done:e,value:l}=await t.read();if(e)break;for(let e of r.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if(e.choices&&e.choices[0]?.delta?.content){let t=e.choices[0].delta.content;n+=t,!o&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(o=!0,i&&(clearTimeout(i),i=null),eh.markOrchestrationStarted()),o&&ef(t,eh);let r=s.content[0];r.text=n,k(e=>e.map(e=>e.id===a?{...e,content:[r]}:e))}}catch(e){}}}if(i&&clearTimeout(i),n&&V){let e={...s,content:[{type:"text",text:n}]};n.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||n.includes("*The response will continue automatically in a new message...*")?(await eP(V.id,e),setTimeout(()=>{eb()},2e3)):await eP(V.id,e)}}else{let t=await e.json(),r="Could not parse assistant's response.";t.choices?.[0]?.message?.content?r=t.choices[0].message.content:t.content?.[0]?.text?r=t.content[0].text:"string"==typeof t.text&&(r=t.text);let a={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:r}]};k(e=>[...e,a]),V&&await eP(V.id,a)}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};k(t=>[...t,e]),T(t.message)}finally{C(!1),eh.markComplete()}},eq=async(e,t)=>{if(!g||e<0||e>=j.length||"assistant"!==j[e].role)return;C(!0),T(null),ep(""),eh.startProcessing();let r=j.slice(0,e);if(k(r),V)try{if(j.slice(e).length>0){let t=j[e],r=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:V.id,from_timestamp:r})});a.ok&&await a.json()}ek(!0)}catch(e){}let a={custom_api_config_id:g,messages:r.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let r=e.content[0];t=r&&"text"===r.type?r.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:E,...t&&{specific_api_key_id:t}};try{eh.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(a),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||`HTTP ${e.status}: ${e.statusText}`)}eh.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),r=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===r&&(er(t),en(!0),ei(!1)),setTimeout(()=>{E&&eh.markStreaming()},400),E&&e.body){let t=e.body.getReader(),r=new TextDecoder,a="",s={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};k(e=>[...e,s]);try{for(;;){let{done:e,value:n}=await t.read();if(e)break;for(let e of r.decode(n,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t);if(e.choices?.[0]?.delta?.content){let t=e.choices[0].delta.content;a+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")?(eh.markOrchestrationStarted(),ef(t,eh)):ex&&ef(t,eh),k(e=>e.map(e=>e.id===s.id?{...e,content:[{type:"text",text:a}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(a&&V){let e={...s,content:[{type:"text",text:a}]};await eP(V.id,e)}}else{let t=await e.json(),r="";t.choices&&t.choices.length>0&&t.choices[0].message?r=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(r=t.content[0].text);let a={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:r}]};k(e=>[...e,a]),V&&await eP(V.id,a)}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};k(t=>[...t,e]),T(t.message),V&&await eP(V.id,e)}finally{C(!1),eh.markComplete()}},eU=async e=>{if(e&&e.preventDefault(),!w.trim()&&0===L.length||!g)return;if("continue"===w.trim().toLowerCase()&&j.length>0){v(""),await eb();return}C(!0),T(null),ep(""),eh.startProcessing(),performance.now();let t=w.trim(),r=[...L],a=[...P];v(""),eA();let s=[],n=[];if(t&&(s.push({type:"text",text:t}),n.push({type:"text",text:t})),r.length>0)try{for(let e=0;e<r.length;e++){let t=r[e],o=a[e],i=await eT(t);s.push({type:"image_url",image_url:{url:o}}),n.push({type:"image_url",image_url:{url:i}})}}catch(e){T("Failed to process one or more images. Please try again."),C(!1),v(t),R(r),I(a);return}let o={id:Date.now().toString(),role:"user",content:s};k(e=>[...e,o]);let i=V?.id,l=Promise.resolve(i);Promise.resolve(),i||V||(l=eR(o)),l.then(async e=>{if(e)return await eP(e,o),e}).catch(e=>{});let d={custom_api_config_id:g,messages:[...j.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let r=e.content[0];t=r&&"text"===r.type?r.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),{role:"user",content:1===n.length&&"text"===n[0].type?n[0].text:n}],stream:E};try{eh.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"},body:JSON.stringify(d),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||`API Error: ${e.statusText} (Status: ${e.status})`)}eh.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),r=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===r&&(er(t),en(!0),ei(!1)),E&&e.body){let t=e.body.getReader(),r=new TextDecoder,a=Date.now().toString()+"-assistant",s={id:a,role:"assistant",content:[{type:"text",text:""}]};k(e=>[...e,s]);let n="",o=!1,i=null;for(i=setTimeout(()=>{o||eh.markStreaming()},400);;){let{done:e,value:l}=await t.read();if(e)break;for(let e of r.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if(e.choices&&e.choices[0]?.delta?.content){let t=e.choices[0].delta.content;n+=t,!o&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(o=!0,i&&(clearTimeout(i),i=null),eh.markOrchestrationStarted()),o&&ef(t,eh);let r=s.content[0];r.text=n,k(e=>e.map(e=>e.id===a?{...e,content:[r]}:e))}}catch(e){}}}if(i&&clearTimeout(i),n){let t={...s,content:[{type:"text",text:n}]},r=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),n.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||n.includes("*The response will continue automatically in a new message...*")?(l.then(async e=>{e&&await eP(e,t)}),setTimeout(()=>{eb()},null!==r?1e3:2e3)):l.then(async e=>{e&&await eP(e,t)})}}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};k(t=>[...t,e]),T(t.message),l.then(async t=>{t&&await eP(t,e)}).catch(e=>{})}finally{C(!1),eh.markComplete(),function(e){if(!(e.length<2)){for(let t=1;t<e.length;t++){let r=e[t],a=e[t-1];r.timestamp,a.timestamp}e[e.length-1].timestamp,e[0].timestamp}}(eh.stageHistory),performance.now(),l.then(async e=>{e&&!V&&ek(!0)}).catch(e=>{})}};return(0,a.jsxs)("div",{className:"min-h-screen bg-[#faf8f5] flex",children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out",style:{marginLeft:n,marginRight:el&&!ec?"50%":H?"0px":"320px"},children:[(0,a.jsx)("div",{className:"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out",style:{left:n,right:el&&!ec?"50%":H?"0px":"320px"},children:(0,a.jsx)("div",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connected"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Not Connected"})]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{value:g,onChange:e=>ez(e.target.value),disabled:0===o.length,className:"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]",children:[(0,a.jsx)("option",{value:"",children:"Select Router"}),o.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Streaming"}),(0,a.jsx)("button",{onClick:()=>A(!E),className:`relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm ${E?"bg-orange-500 shadow-orange-200":"bg-gray-300"}`,children:(0,a.jsx)("span",{className:`inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${E?"translate-x-6":"translate-x-1"}`})})]})]})})}),(0,a.jsx)("div",{className:"flex-1 flex flex-col pt-20 pb-32",children:0!==j.length||V?(0,a.jsxs)("div",{className:`flex-1 relative ${el&&!ec?"overflow-visible":"overflow-hidden"}`,children:[(0,a.jsx)("div",{className:`h-full flex ${el&&!ec?"justify-start":"justify-center"}`,children:(0,a.jsx)("div",{ref:D,className:`w-full h-full overflow-y-auto px-6 transition-all duration-300 ${el&&!ec?"max-w-2xl -ml-32":"max-w-4xl"}`,onScroll:e=>{let t=e.currentTarget;M(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&j.length>0)},children:(0,a.jsxs)("div",{className:"space-y-6 py-8",children:[V&&j.length>=50&&(0,a.jsx)("div",{className:"text-center py-4",children:(0,a.jsx)("button",{onClick:()=>eM(V,!0),disabled:ew,className:"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50",children:ew?"Loading...":"Load Earlier Messages"})}),Q&&0===j.length&&(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,a.jsxs)("div",{className:"flex justify-start",children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,a.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),j.map((e,t)=>(0,a.jsxs)("div",{className:`flex ${"user"===e.role?"justify-end":"justify-start"} group ${el&&!ec?"-ml-96":""} ${"assistant"===e.role&&el&&!ec?"ml-8":""}`,children:["assistant"===e.role&&(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsxs)("div",{className:`${"user"===e.role?el&&!ec?"max-w-[60%]":"max-w-[50%]":el&&!ec?"max-w-[85%]":"max-w-[75%]"} relative ${"user"===e.role?"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm":"assistant"===e.role?"card text-gray-900 rounded-2xl rounded-bl-lg":"system"===e.role?"bg-amber-50 text-amber-800 rounded-xl border border-amber-200":"bg-red-50 text-red-800 rounded-xl border border-red-200"} px-4 py-3 transition-all duration-300`,children:["user"===e.role&&(0,a.jsxs)("div",{className:"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,a.jsx)(x.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer"}),(0,a.jsx)("button",{onClick:()=>eH(e.id,e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,a.jsx)(m,{className:"w-4 h-4 stroke-2"})})]}),"user"!==e.role&&(0,a.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,a.jsx)(x.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===e.role&&g&&(0,a.jsx)(y,{configId:g,onRetry:e=>eq(t,e),disabled:N})]}),(0,a.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===e.role&&Z===e.id?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:G,onChange:e=>X(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:eF,disabled:!G.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,a.jsx)(i,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save & Continue"})]}),(0,a.jsxs)("button",{onClick:eB,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Cancel"})]})]}),(0,a.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):e.content.map((t,r)=>{if("text"===t.type)if("assistant"===e.role)return(0,a.jsx)(h,{content:t.text,className:"text-sm"},r);else return(0,a.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-sm",children:t.text},r);return"image_url"===t.type?(0,a.jsx)("img",{src:t.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},r):null})})]}),"user"===e.role&&(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]},e.id)),es&&et&&ec&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(W,{orchestrationComplete:eo,onMaximize:()=>{eg(!0),setTimeout(()=>eg(!1),100)},isCanvasOpen:el,isCanvasMinimized:ec})}),N&&(0,a.jsx)($,{currentStage:eh.currentStage,isStreaming:E&&"typing"===eh.currentStage,orchestrationStatus:ex,onStageChange:e=>{}}),es&&et&&(0,a.jsx)(F,{executionId:et,onCanvasStateChange:(e,t)=>{ed(e),em(t),e&&!t&&B(!0)},forceMaximize:eu,onComplete:e=>{if(et?.startsWith("test-execution-id"))return void ei(!0);ei(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};k(e=>[...e,t]),en(!1),er(null),ei(!1),V?.id&&eP(V.id,t).catch(e=>{})},onError:e=>{et?.startsWith("test-execution-id")||(T(`Orchestration error: ${e}`),en(!1),er(null))}}),(0,a.jsx)("div",{ref:z})]})})}),_&&(0,a.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,a.jsx)("button",{onClick:()=>e_(!0),className:"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 overflow-hidden",children:(0,a.jsx)("div",{className:`w-full mx-auto transition-all duration-300 ${el&&!ec?"max-w-2xl -ml-32":"max-w-4xl"}`,children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to RoKey"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-md mx-auto",children:"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4 w-full max-w-2xl",children:[{id:"write-copy",title:"Write copy",description:"Create compelling marketing content",icon:"✍️",color:"bg-amber-100 text-amber-700",prompt:"Help me write compelling copy for my product landing page"},{id:"image-generation",title:"Image generation",description:"Create visual content descriptions",icon:"\uD83C\uDFA8",color:"bg-blue-100 text-blue-700",prompt:"Help me create detailed prompts for AI image generation"},{id:"create-avatar",title:"Create avatar",description:"Design character personas",icon:"\uD83D\uDC64",color:"bg-green-100 text-green-700",prompt:"Help me create a detailed character avatar for my story"},{id:"write-code",title:"Write code",description:"Generate and debug code",icon:"\uD83D\uDCBB",color:"bg-purple-100 text-purple-700",prompt:"Help me write clean, efficient code for my project"}].map(e=>(0,a.jsxs)("button",{onClick:()=>eI(e.prompt),disabled:!g,className:`group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ${!g?"cursor-not-allowed":"cursor-pointer hover:scale-[1.02]"}`,children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-xl flex items-center justify-center text-xl ${e.color} group-hover:scale-110 transition-transform duration-200`,children:e.icon}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),(0,a.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,a.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out",style:{left:n,right:el&&!ec?"50%":H?"0px":"320px"},children:(0,a.jsx)("div",{className:"px-6 pt-3 pb-2 flex justify-center",children:(0,a.jsxs)("div",{className:`w-full transition-all duration-300 ${el&&!ec?"max-w-2xl":"max-w-4xl"}`,children:[S&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"text-red-800 text-sm font-medium",children:S})]})}),!1,(0,a.jsxs)("form",{onSubmit:eU,children:[P.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[P.length," image",P.length>1?"s":""," attached"]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>eA(),className:"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:P.map((e,t)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,a.jsx)("img",{src:e,alt:`Preview ${t+1}`,className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,a.jsx)("button",{type:"button",onClick:()=>eA(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":`Remove image ${t+1}`,children:(0,a.jsx)(l,{className:"w-3.5 h-3.5"})})]}),(0,a.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,a.jsx)("div",{className:"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300",children:(0,a.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:eE,ref:O,className:"hidden",id:"imageUpload"}),(0,a.jsxs)("button",{type:"button",onClick:()=>O.current?.click(),disabled:L.length>=10,className:`relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ${L.length>=10?"text-gray-300 cursor-not-allowed":"text-gray-400 hover:text-orange-500 hover:bg-orange-50"}`,"aria-label":L.length>=10?"Maximum 10 images reached":"Attach images",title:L.length>=10?"Maximum 10 images reached":"Attach images (up to 10)",children:[(0,a.jsx)(d,{className:"w-5 h-5"}),L.length>0&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:L.length})]}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("textarea",{value:w,onChange:e=>v(e.target.value),placeholder:g?"Type a message...":"Select a router first",disabled:!g||N,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(w.trim()||L.length>0)&&g&&!N&&eU())},style:{minHeight:"24px",maxHeight:"120px"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,a.jsx)("button",{type:"submit",disabled:!g||N||!w.trim()&&0===L.length,className:"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0","aria-label":"Send message",title:"Send message",children:N?(0,a.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):(0,a.jsx)(c,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,a.jsx)("div",{className:`fixed top-0 right-0 h-full bg-white border-l border-gray-200/50 shadow-xl transition-all duration-300 ease-in-out z-30 ${H?"w-0 overflow-hidden":"w-80"}`,style:{transform:H?"translateX(100%)":"translateX(0)",opacity:+!H},children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200/50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"font-semibold text-gray-900",children:"History"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[ey.length," conversations"]})]})]}),(0,a.jsx)("button",{onClick:()=>B(!H),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105","aria-label":"Toggle history sidebar",children:(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,a.jsx)("div",{className:"p-4 border-b border-gray-200/50",children:(0,a.jsxs)("button",{onClick:eO,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,a.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:ew?(0,a.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,a.jsxs)("div",{className:"p-3 rounded-xl border border-gray-100 animate-pulse",children:[(0,a.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded mb-2"}),(0,a.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"})]},t))}):0===ey.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No conversations yet"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Start chatting to see your history"})]}):(0,a.jsx)(a.Fragment,{children:ey.map(e=>(0,a.jsx)(ea,{chat:e,currentConversation:V,onLoadChat:eD,onDeleteChat:e$},e.id))})}),ev&&(0,a.jsx)("div",{className:"px-4 py-2 bg-orange-50 border-t border-orange-100",children:(0,a.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,a.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),ej&&(0,a.jsx)("div",{className:"px-4 py-2 bg-red-50 border-t border-red-100",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,a.jsx)("span",{children:"Failed to load history"}),(0,a.jsx)("button",{onClick:()=>ek(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),(0,a.jsx)("div",{className:`fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ${H?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"}`,children:(0,a.jsx)("button",{onClick:()=>B(!1),className:"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105","aria-label":"Show history sidebar",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}ea.displayName="ChatHistoryItem"},71178:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))})},72132:(e,t,r)=>{Promise.resolve().then(r.bind(r,35291))},73136:e=>{"use strict";e.exports=require("node:url")},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},89530:(e,t,r)=>{Promise.resolve().then(r.bind(r,21590))},90692:(e,t,r)=>{Promise.resolve().then(r.bind(r,47417))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,8153,1658,9906,8947,2646],()=>r(54759));module.exports=a})();