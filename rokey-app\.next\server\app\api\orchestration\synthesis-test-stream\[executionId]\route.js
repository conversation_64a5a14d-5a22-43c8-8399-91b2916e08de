(()=>{var e={};e.id=8644,e.ids=[8644],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59608:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83885:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>a,serverHooks:()=>c,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>u});var s=r(96559),o=r(48088),i=r(37719),n=r(59608);let a=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/orchestration/synthesis-test-stream/[executionId]/route",pathname:"/api/orchestration/synthesis-test-stream/[executionId]",filename:"route",bundlePath:"app/api/orchestration/synthesis-test-stream/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\synthesis-test-stream\\[executionId]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:u,serverHooks:c}=a;function d(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:u})}},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719],()=>r(83885));module.exports=s})();