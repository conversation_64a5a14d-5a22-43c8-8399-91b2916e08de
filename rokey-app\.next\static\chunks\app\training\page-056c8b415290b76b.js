(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7637],{3408:(e,r,t)=>{"use strict";function n(){let[e,r]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[t,n]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[o,i]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[a,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[d,s]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[l,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[m,O]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),f=async e=>{if(e)try{let t=await fetch("/api/training/jobs?custom_api_config_id=".concat(e));if(t.ok){let e=await t.json();if(e.length>0){var r;let t=e[0];(null==(r=t.training_data)?void 0:r.raw_prompts)&&O(t.training_data.raw_prompts)}}}catch(e){}};Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{(async()=>{try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch configurations");let t=await e.json();r(t),t.length>0&&(n(t[0].id),f(t[0].id))}catch(e){s("Failed to load configurations: ".concat(e.message))}})()},[]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t&&f(t)},[t]);let h=e=>{let r={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let t of e.split("\n").filter(e=>e.trim())){let e=t.trim();if(e.startsWith("SYSTEM:"))r.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))r.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let t=e.includes("→")?"→":"->",n=e.split(t);if(n.length>=2){let e=n[0].trim(),o=n.slice(1).join(t).trim();r.examples.push({input:e,output:o})}}else e.length>0&&(r.general_instructions+=e+"\n")}return r},N=async()=>{if(!t||!m.trim())return void s("Please select an API configuration and provide training prompts.");if(!a){c(!0),s(null),u(null);try{var r;let n=h(m),o=(null==(r=e.find(e=>e.id===t))?void 0:r.name)||"Unknown Config",i={custom_api_config_id:t,name:"".concat(o," Training - ").concat(new Date().toLocaleDateString()),description:"Training job for ".concat(o," with ").concat(n.examples.length," examples"),training_data:{processed_prompts:n,raw_prompts:m.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},a=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!a.ok){let e=await a.text();throw Error("Failed to save training job: ".concat(a.status," ").concat(e))}let c=await a.json(),d="updated"===c.operation,s="".concat(d?"\uD83D\uDD04":"\uD83C\uDF89"," Prompt Engineering ").concat(d?"updated":"completed"," successfully!\n\n")+'Your "'.concat(o,'" configuration has been ').concat(d?"updated":"enhanced"," with:\n")+"• ".concat(n.examples.length," training examples\n")+"• Custom system instructions and behavior guidelines\n\n✨ All future chats using this configuration will automatically:\n• Follow your training examples\n• Apply your behavior guidelines\n• Maintain consistent personality and responses\n\n"+"\uD83D\uDE80 Try it now in the Playground to see your ".concat(d?"updated":"enhanced"," model in action!\n\n")+"\uD83D\uDCA1 Your training prompts remain here so you can modify them anytime.";u(s)}catch(e){s("Failed to create prompt engineering: ".concat(e.message))}finally{c(!1)}}};return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"max-w-6xl mx-auto",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mb-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Prompt Engineering"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-lg text-gray-600 max-w-3xl",children:"Create custom prompts to enhance your AI models with specific instructions, behavior guidelines, and examples."})]}),d&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-xl p-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-2",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-800 text-sm font-medium",children:d})]})}),l&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-xl p-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-2",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-green-800 text-sm font-medium",children:l})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Create Custom Prompts"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Configuration"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"configSelect",value:t,onChange:e=>n(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",children:"Choose which model to train..."}),e.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.id,children:e.name},e.id))]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Prompts & Instructions"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("textarea",{id:"trainingPrompts",value:m,onChange:e=>O(e.target.value),placeholder:"Enter your training prompts using these formats:\n\nSYSTEM: You are a helpful customer service agent for our company\nBEHAVIOR: Always be polite and offer solutions\n\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\n\nGeneral instructions can be written as regular text.",rows:12,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Training Format Guide:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("ul",{className:"text-xs text-blue-800 space-y-1",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("li",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex space-x-3",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"button",className:"btn-secondary",onClick:()=>{confirm("Clear all training prompts?")&&O("")},children:"Clear Form"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"button",onClick:N,disabled:!t||!m.trim()||a,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:a?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})]})})}t.r(r),t.d(r,{default:()=>n}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}()},42005:(e,r,t)=>{Promise.resolve().then(t.bind(t,3408))}},e=>{var r=r=>e(e.s=r);e.O(0,[6642,7706,7544,2138,8899,5495,7358],()=>r(42005)),_N_E=e.O()}]);