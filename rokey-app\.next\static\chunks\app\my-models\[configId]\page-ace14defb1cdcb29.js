(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{38152:(e,r,t)=>{"use strict";t.d(r,{Pi:()=>n.A,fK:()=>a.A,uc:()=>o.A});var n=t(55628),o=t(31151),a=t(74500)},44469:(e,r,t)=>{Promise.resolve().then(t.bind(t,76357))},75922:(e,r,t)=>{"use strict";t.d(r,{MG:()=>n});let n=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76357:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var n=t(35695),o=t(75922);let a=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.)."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.)."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models."}],i=e=>a.find(r=>r.id===e);var c=t(32461),d=t(6865),l=t(89959),s=t(37186),u=t(67695),m=t(94038),O=t(61316),f=t(85037),h=t(57765),N=t(8246),_=t(31151),D=t(52589),b=t(55424),E=t(80377),U=t(87162),v=t(28003),j=t(79958),x=t(53951),w=t(99323);!function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();let g=o.MG.map(e=>({value:e.id,label:e.name}));function p(){var e,r;let t=(0,n.useParams)().configId,p=(0,U.Z)(),C=(0,w.bu)(),T=(null==C?void 0:C.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:F,isCached:M}=(0,v._)(),{createHoverPrefetch:y}=(0,x.c)(),[L,k]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[I,A]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[P,S]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[R,K]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())((null==(e=g[0])?void 0:e.value)||"openai"),[V,B]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[G,z]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[J,H]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[q,W]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(1),[Z,Q]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[Y,$]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[X,ee]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[er,et]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[en,eo]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[ea,ei]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[ec,ed]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[el,es]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!0),[eu,em]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[eO,ef]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[eh,eN]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[e_,eD]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[eb,eE]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(1),[eU,ev]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[ej,ex]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[ew,eg]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[ep,eC]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[eT,eF]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[eM,ey]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[eL,ek]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[eI,eA]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[eP,eS]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(""),[eR,eK]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[eV,eB]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[eG,ez]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),eJ=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async()=>{if(!t)return;let e=F(t);if(e&&e.configDetails){k(e.configDetails),A(!1);return}M(t)||S(!0),A(!0),$(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to fetch configurations list")}let r=(await e.json()).find(e=>e.id===t);if(!r)throw Error("Configuration not found in the list.");k(r)}catch(e){$("Error loading model configuration: ".concat(e.message)),k(null)}finally{A(!1),S(!1)}},[t,F,M]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{eJ()},[eJ]);let eH=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async()=>{let e=F(t);if(e&&e.models){et(e.models),eo(!1);return}eo(!0),ei(null),et(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),r=await e.json();if(!e.ok)throw Error(r.error||"Failed to fetch models from database.");r.models?et(r.models):et([])}catch(e){ei("Error fetching models: ".concat(e.message)),et([])}finally{eo(!1)}},[t,F]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t&&eH()},[t,eH]);let eq=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async()=>{let e=F(t);if(e&&e.userCustomRoles){eg(e.userCustomRoles),eC(!1);return}eC(!0),eF(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let r=await e.json();eg(r)}else{let r;try{r=await e.json()}catch(t){r={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let t=r.error||(r.issues?JSON.stringify(r.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eF(t);else throw Error(t);eg([])}}catch(e){eF(e.message),eg([])}finally{eC(!1)}},[]),eW=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(async()=>{if(!t||!ew)return;let e=F(t);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let r=e.apiKeys.map(async r=>{let t=await fetch("/api/keys/".concat(r.id,"/roles")),n=[];return t.ok&&(n=(await t.json()).map(e=>{let r=i(e.role_name);if(r)return r;let t=ew.find(r=>r.role_id===e.role_name);return t?{id:t.role_id,name:t.name,description:t.description||void 0}:null}).filter(Boolean)),{...r,assigned_roles:n,is_default_chat_model:e.defaultChatKeyId===r.id}});ed(await Promise.all(r)),ef(e.defaultChatKeyId),es(!1);return}es(!0),$(e=>e&&e.startsWith("Error loading model configuration:")?e:null),ee(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(t));if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to fetch API keys")}let r=await e.json(),n=await fetch("/api/custom-configs/".concat(t,"/default-chat-key"));n.ok;let o=200===n.status?await n.json():null;ef((null==o?void 0:o.id)||null);let a=r.map(async e=>{let r=await fetch("/api/keys/".concat(e.id,"/roles")),t=[];return r.ok&&(t=(await r.json()).map(e=>{let r=i(e.role_name);if(r)return r;let t=ew.find(r=>r.role_id===e.role_name);return t?{id:t.role_id,name:t.name,description:t.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:t,is_default_chat_model:(null==o?void 0:o.id)===e.id}}),c=await Promise.all(a);ed(c)}catch(e){$(r=>r?"".concat(r,"; ").concat(e.message):e.message)}finally{es(!1)}},[t,ew]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{L&&eq()},[L,eq]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{L&&ew&&eW()},[L,ew,eW]);let eZ=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(er){let e=o.MG.find(e=>e.id===R);if(!e)return[];if("openrouter"===e.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,r)=>(e.label||"").localeCompare(r.label||""));if("deepseek"===e.id){let e=[];return er.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),er.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,r)=>(e.label||"").localeCompare(r.label||""))}return er.filter(r=>r.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,r)=>(e.label||"").localeCompare(r.label||""))}return[]},[er,R]),eQ=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(er&&e_){let e=o.MG.find(e=>e.id===e_.provider);if(!e)return[];if("openrouter"===e.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,r)=>(e.label||"").localeCompare(r.label||""));if("deepseek"===e.id){let e=[];return er.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),er.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,r)=>(e.label||"").localeCompare(r.label||""))}return er.filter(r=>r.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,r)=>(e.label||"").localeCompare(r.label||""))}return[]},[er,e_]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{eZ.length>0?B(eZ[0].value):B("")},[eZ,R]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{R&&eH()},[R,eH]);let eY=async e=>{if(e.preventDefault(),!t)return void $("Configuration ID is missing.");if(ec.some(e=>e.predefined_model_id===V))return void $("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");Q(!0),$(null),ee(null);let r=[...ec];try{var n;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:t,provider:R,predefined_model_id:V,api_key_raw:G,label:J,temperature:q})}),r=await e.json();if(!e.ok)throw Error(r.details||r.error||"Failed to save API key");let o={id:r.id,custom_api_config_id:t,provider:R,predefined_model_id:V,label:J,temperature:q,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_chat_model:!1,assigned_roles:[]};ed(e=>[...e,o]),ee('API key "'.concat(J,'" saved successfully!')),K((null==(n=g[0])?void 0:n.value)||"openai"),z(""),H(""),W(1),eZ.length>0&&B(eZ[0].value)}catch(e){ed(r),$("Save Key Error: ".concat(e.message))}finally{Q(!1)}},e$=e=>{eD(e),eE(e.temperature||1),ev(e.predefined_model_id)},eX=async()=>{if(!e_)return;if(ec.some(e=>e.id!==e_.id&&e.predefined_model_id===eU))return void $("This model is already configured in this setup. Each model can only be used once per configuration.");ex(!0),$(null),ee(null);let e=[...ec];ed(e=>e.map(e=>e.id===e_.id?{...e,temperature:eb,predefined_model_id:eU}:e));try{let r=await fetch("/api/keys?id=".concat(e_.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:eb,predefined_model_id:eU})}),t=await r.json();if(!r.ok)throw ed(e),Error(t.details||t.error||"Failed to update API key");ee('API key "'.concat(e_.label,'" updated successfully!')),eD(null)}catch(e){$("Update Key Error: ".concat(e.message))}finally{ex(!1)}},e0=(e,r)=>{p.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(r,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{em(e),$(null),ee(null);let t=[...ec],n=ec.find(r=>r.id===e);ed(r=>r.filter(r=>r.id!==e)),(null==n?void 0:n.is_default_chat_model)&&ef(null);try{let n=await fetch("/api/keys/".concat(e),{method:"DELETE"}),o=await n.json();if(!n.ok){if(ed(t),ef(eO),404===n.status){ed(r=>r.filter(r=>r.id!==e)),ee('API key "'.concat(r,'" was already deleted.'));return}throw Error(o.details||o.error||"Failed to delete API key")}ee('API key "'.concat(r,'" deleted successfully!'))}catch(e){throw $("Delete Key Error: ".concat(e.message)),e}finally{em(null)}})},e2=async e=>{if(!t)return;$(null),ee(null);let r=[...ec];ed(r=>r.map(r=>({...r,is_default_chat_model:r.id===e}))),ef(e);try{let n=await fetch("/api/custom-configs/".concat(t,"/default-key-handler/").concat(e),{method:"PUT"}),o=await n.json();if(!n.ok)throw ed(r.map(e=>({...e}))),ef(eO),Error(o.details||o.error||"Failed to set default chat key");ee(o.message||"Default general chat key updated!")}catch(e){$("Set Default Error: ".concat(e.message))}},e1=async(e,r,t)=>{$(null),ee(null);let n="/api/keys/".concat(e.id,"/roles"),o=[...a.map(e=>({...e,isCustom:!1})),...ew.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===r)||{id:r,name:r,description:""},i=ec.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),c=null;eh&&eh.id===e.id&&(c={...eh,assigned_roles:[...eh.assigned_roles.map(e=>({...e}))]}),ed(n=>n.map(n=>{if(n.id===e.id){let e=t?n.assigned_roles.filter(e=>e.id!==r):[...n.assigned_roles,o];return{...n,assigned_roles:e}}return n})),eh&&eh.id===e.id&&eN(e=>{if(!e)return null;let n=t?e.assigned_roles.filter(e=>e.id!==r):[...e.assigned_roles,o];return{...e,assigned_roles:n}});try{let a;a=t?await fetch("".concat(n,"/").concat(r),{method:"DELETE"}):await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:r})});let d=await a.json();if(!a.ok){if(ed(i),c)eN(c);else if(eh&&eh.id===e.id){let r=i.find(r=>r.id===e.id);r&&eN(r)}let r=409===a.status&&d.error?d.error:d.details||d.error||(t?"Failed to unassign role":"Failed to assign role");throw Error(r)}ee(d.message||"Role '".concat(o.name,"' ").concat(t?"unassigned":"assigned"," successfully."))}catch(e){$("Role Update Error: ".concat(e.message))}},e5=async()=>{if(!eL.trim()||eL.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eL.trim()))return void eB("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(a.some(e=>e.id.toLowerCase()===eL.trim().toLowerCase())||ew.some(e=>e.role_id.toLowerCase()===eL.trim().toLowerCase()))return void eB("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eI.trim())return void eB("Role Name is required.");eB(null),eK(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eL.trim(),name:eI.trim(),description:eP.trim()})});if(!e.ok){let r;try{r=await e.json()}catch(n){let t=await e.text().catch(()=>"HTTP status ".concat(e.status));r={error:"Server error, could not parse response.",details:t}}let t=r.error||"Failed to create custom role.";if(r.details)t+=" (Details: ".concat(r.details,")");else if(r.issues){let e=Object.entries(r.issues).map(e=>{let[r,t]=e;return"".concat(r,": ").concat(t.join(", "))}).join("; ");t+=" (Issues: ".concat(e,")")}throw Error(t)}let r=await e.json();ek(""),eA(""),eS(""),eq(),ee("Custom role '".concat(r.name,"' created successfully! It is now available globally."))}catch(e){eB(e.message)}finally{eK(!1)}},e6=(e,r)=>{e&&p.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(r,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{ez(e),eF(null),eB(null),ee(null);try{let n=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),o=await n.json();if(!n.ok)throw Error(o.error||"Failed to delete custom role");eg(r=>r.filter(r=>r.id!==e)),ee(o.message||'Global custom role "'.concat(r,'" deleted successfully.')),t&&eW()}catch(e){throw eF("Error deleting role: ".concat(e.message)),e}finally{ez(null)}})};return P&&!M(t)?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(j.A,{}):I&&!L?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(j._,{}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"min-h-screen",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mb-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>T("/my-models"),className:"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex-1",children:L?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center mb-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(s.A,{className:"h-6 w-6 text-white"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:"text-3xl font-bold text-gray-900",children:L.name}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-sm text-gray-500 mt-1",children:"Model Configuration"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",L.id]})]}):Y&&!I?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(D.A,{className:"h-6 w-6 text-red-600"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:"text-2xl font-bold text-red-600",children:"Configuration Error"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-500 mt-1",children:Y.replace("Error loading model configuration: ","")})]})]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(l.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h1",{className:"text-2xl font-bold text-gray-900",children:"Loading Configuration..."}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-500 mt-1",children:"Please wait while we fetch your model details"})]})]})}),L&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>T("/routing-setup/".concat(t,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...y(t),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(s.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})]})}),X&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(d.A,{className:"h-5 w-5 text-green-600"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-green-800 font-medium",children:X})]})}),Y&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(D.A,{className:"h-5 w-5 text-red-600"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-800 font-medium",children:Y})]})})]}),L&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"xl:col-span-2",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center mb-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(h.A,{className:"h-5 w-5 text-white"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:"text-xl font-bold text-gray-900",children:"Add API Key"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-500",children:"Configure new key"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("form",{onSubmit:eY,className:"space-y-5",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"provider",value:R,onChange:e=>{K(e.target.value)},className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:g.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value))})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{id:"apiKeyRaw",type:"password",value:G,onChange:e=>z(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"Enter your API key"}),en&&null===er&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(l.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),ea&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg",children:ea})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Variant"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"predefinedModelId",value:V,onChange:e=>B(e.target.value),disabled:!eZ.length,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500",children:eZ.length>0?eZ.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value)):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",disabled:!0,children:null===er&&en?"Loading models...":"Select a provider first"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-700 mb-2",children:"Label"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",id:"label",value:J,onChange:e=>H(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"text-xs text-gray-500 ml-1",children:"(0.0 - 2.0)"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-2",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:q,onChange:e=>W(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-between items-center",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"text-xs text-gray-500",children:"Conservative"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-2",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"number",min:"0",max:"2",step:"0.1",value:q,onChange:e=>W(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"text-xs text-gray-500",children:"Creative"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-500",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{type:"submit",disabled:Z||!V||""===V||!G.trim()||!J.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:Z?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"flex items-center justify-center",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(l.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"flex items-center justify-center",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(h.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-start space-x-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(u.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Key Configuration Rules"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"text-xs text-blue-800 space-y-1",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["✅ ",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Same API key, different models:"})," Allowed"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["✅ ",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Different API keys, same model:"})," Allowed"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{children:["❌ ",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"xl:col-span-3",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center mb-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(m.A,{className:"h-5 w-5 text-white"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:"text-xl font-bold text-gray-900",children:"API Keys & Roles"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-500",children:"Manage existing keys"})]})]}),el&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"text-center py-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(l.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-600 text-sm",children:"Loading API keys..."})]}),!el&&0===ec.length&&(!Y||Y&&Y.startsWith("Error loading model configuration:"))&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"text-center py-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(m.A,{className:"h-6 w-6 text-gray-400"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!el&&ec.length>0&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:ec.map((e,r)=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*r,"ms")},children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-start justify-between",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex-1 min-w-0",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center mb-2",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-sm font-semibold text-gray-900 truncate mr-2",children:e.label}),e.is_default_chat_model&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(N.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-2",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-2",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border",children:[e.provider," (",e.predefined_model_id,")"]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200",children:["Temp: ",e.temperature]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800",children:e.name},e.id)):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border",children:"No roles"})})]}),!e.is_default_chat_model&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>e2(e.id),className:"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"tooltip-set-default-".concat(e.id),"data-tooltip-content":"Set as default chat model",children:["Set Default",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(b.m_,{id:"tooltip-set-default-".concat(e.id),place:"top"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>e$(e),disabled:eu===e.id,className:"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-edit-".concat(e.id),"data-tooltip-content":"Edit Model & Settings",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(O.A,{className:"h-4 w-4"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(b.m_,{id:"tooltip-edit-".concat(e.id),place:"top"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>eN(e),disabled:eu===e.id,className:"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-roles-".concat(e.id),"data-tooltip-content":"Manage Roles",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(s.A,{className:"h-4 w-4"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(b.m_,{id:"tooltip-roles-".concat(e.id),place:"top"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>e0(e.id,e.label),disabled:eu===e.id,className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-delete-".concat(e.id),"data-tooltip-content":"Delete Key",children:[eu===e.id?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(_.A,{className:"h-4 w-4 animate-pulse"}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(_.A,{className:"h-4 w-4"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(b.m_,{id:"tooltip-delete-".concat(e.id),place:"top"})]})]})]})},e.id))}),!el&&Y&&!Y.startsWith("Error loading model configuration:")&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center space-x-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(D.A,{className:"h-5 w-5 text-red-600"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-800 font-medium text-sm",children:["Could not load API keys/roles: ",Y]})]})})]})})]}),eh&&(()=>{if(!eh)return null;let e=[...a.map(e=>({...e,isCustom:!1})),...ew.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,r)=>e.name.localeCompare(r.name));return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"card w-full max-w-lg max-h-[90vh] flex flex-col",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage Roles for: ",Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"text-orange-600",children:eh.label})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{eN(null),ey(!1),eB(null)},className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(D.A,{className:"h-6 w-6"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"p-6 border-b border-gray-200",children:[eT&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eT]})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-end mb-4",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>ey(!eM),className:"btn-primary text-sm inline-flex items-center",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(f.A,{className:"h-4 w-4 mr-2"}),eM?"Cancel New Role":"Create New Custom Role"]})}),eM&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Create New Custom Role for this Configuration"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",id:"newCustomRoleId",value:eL,onChange:e=>ek(e.target.value.replace(/\s/g,"")),className:"form-input",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name (max 100 chars)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"text",id:"newCustomRoleName",value:eI,onChange:e=>eA(e.target.value),className:"form-input",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (optional, max 500 chars)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("textarea",{id:"newCustomRoleDescription",value:eP,onChange:e=>eS(e.target.value),rows:2,className:"form-input",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eV&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-red-800 text-sm",children:eV})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:e5,disabled:eR,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eR?"Saving Role...":"Save Custom Role"})]})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"p-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select roles to assign:"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[ep&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center justify-center py-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-gray-600 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let r=eh.assigned_roles.some(r=>r.id===e.id);return Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(r?"bg-orange-50 border-orange-200 shadow-sm":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"),children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"checkbox",id:"role-".concat(e.id),checked:r,onChange:()=>e1(eh,e.id,r),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"ml-3 text-sm font-medium ".concat(r?"text-orange-800":"text-gray-900"),children:e.name}),e.isCustom&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Custom"})]}),e.isCustom&&e.databaseId&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>e6(e.databaseId,e.name),disabled:eG===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eG===e.databaseId?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(s.A,{className:"h-4 w-4 animate-spin"}):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(_.A,{className:"h-4 w-4"})})]},e.id)})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-end",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>{eN(null),ey(!1),eB(null)},className:"btn-secondary",children:"Done"})})})]})})})(),e_&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"card w-full max-w-lg",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit API Key"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>eD(null),className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(D.A,{className:"h-6 w-6"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"p-6",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"mb-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:e_.label}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-sm text-gray-600",children:["Current: ",e_.provider," (",e_.predefined_model_id,")"]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"space-y-4",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700",children:(null==(r=o.MG.find(e=>e.id===e_.provider))?void 0:r.name)||e_.provider}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-500 mt-1",children:"Provider cannot be changed"})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("select",{id:"editModelId",value:eU,onChange:e=>ev(e.target.value),disabled:!eQ.length,className:"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100",children:eQ.length>0?eQ.map(e=>Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:e.value,children:e.label},e.value)):Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("option",{value:"",disabled:!0,children:en?"Loading models...":"No models available"})})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature: ",eb]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:eb,onChange:e=>eE(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:"0.0 (Focused)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:"1.0 (Balanced)"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("span",{children:"2.0 (Creative)"})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-gray-50 rounded-lg p-3",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-xs text-gray-600",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"flex justify-end space-x-3",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>eD(null),className:"btn-secondary",disabled:ej,children:"Cancel"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:eX,disabled:ej,className:"btn-primary",children:ej?Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!L&&!I&&!Y&&Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(u.A,{className:"h-8 w-8 text-gray-400"})}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())("button",{onClick:()=>T("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(c.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(E.A,{isOpen:p.isOpen,onClose:p.hideConfirmation,onConfirm:p.onConfirm,title:p.title,message:p.message,confirmText:p.confirmText,cancelText:p.cancelText,type:p.type,isLoading:p.isLoading}),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(b.m_,{id:"global-tooltip"})]})}},99323:(e,r,t)=>{"use strict";t.d(r,{bu:()=>i,i9:()=>a}),function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}();var n=t(35695);let o=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(void 0);function a(e){let{children:r}=e,[t,a]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),[i,c]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),[d,l]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),[s,u]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(new Set),[m,O]=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(!1),f=(0,n.usePathname)(),h=(0,n.useRouter)(),N=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),_=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())([]),D=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(null),b=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(0),E=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({}),U=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())({});Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{O(!0)},[]);let v=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{},[m]);Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{f&&!d.includes(f)&&(l(e=>[...e,f]),u(e=>new Set([...e,f])))},[f,d]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{v("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(i,", current=").concat(f,", navigationId=").concat(D.current)),i&&D.current&&f===i&&(v("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(i," -> ").concat(f)),N.current&&(clearTimeout(N.current),N.current=null),a(!1),c(null),D.current=null,_.current=_.current.filter(e=>e.route!==i))},[f,i,v]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{t&&i&&f===i&&(v("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),a(!1),c(null),N.current&&(clearTimeout(N.current),N.current=null))},[f,i,t,v]);let j=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>s.has(e),[s]),x=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(0===_.current.length)return;let e=_.current[_.current.length-1];_.current=[e];let{route:r,id:t}=e;v("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(r," (id: ").concat(t,")")),N.current&&(clearTimeout(N.current),N.current=null),D.current=t;let n=j(r);n&&(v("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(r)),setTimeout(()=>{D.current===t&&a(!1)},100));try{h.push(r)}catch(e){v("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(r,", using fallback")),window.location.href=r;return}N.current=setTimeout(()=>{if(v("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(r," (id: ").concat(t,"), current path: ").concat(f)),D.current===t){v("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(r));try{window.location.href=r}catch(e){v("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}a(!1),c(null),D.current=null}N.current=null},n?800:3e3)},[h,f,j,v]),w=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(e=>{if(f===e||!m)return;let r=Date.now();if(r-b.current<100&&i===e)return void v("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(b.current=r,E.current[e]||(E.current[e]=0),E.current[e]++,U.current[e]&&clearTimeout(U.current[e]),U.current[e]=setTimeout(()=>{E.current[e]=0},2e3),E.current[e]>=3){v("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),E.current[e]=0,window.location.href=e;return}N.current&&(clearTimeout(N.current),N.current=null),a(!0),c(e);let t="nav_".concat(r,"_").concat(Math.random().toString(36).substr(2,9));_.current=[{route:e,timestamp:r,id:t}],x()},[f,i,x,v,m]),g=Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{N.current&&(clearTimeout(N.current),N.current=null),a(!1),c(null),D.current=null,_.current=[]},[]);return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>{if(!m)return;let e=()=>{!document.hidden&&t&&(v("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{i&&f===i&&(v("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),a(!1),c(null),N.current&&(clearTimeout(N.current),N.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[t,i,f,v,m]),Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(()=>()=>{N.current&&clearTimeout(N.current)},[]),Object(function(){var e=Error("Cannot find module 'react/jsx-runtime'");throw e.code="MODULE_NOT_FOUND",e}())(o.Provider,{value:{isNavigating:t,targetRoute:i,navigateOptimistically:w,clearNavigation:g,isPageCached:j,navigationHistory:d},children:r})}function i(){return Object(function(){var e=Error("Cannot find module 'react'");throw e.code="MODULE_NOT_FOUND",e}())(o)||null}}},e=>{var r=r=>e(e.s=r);e.O(0,[7874,274,5738,1486,2662,8669,8848,4696,9173,9628,6642,7706,7544,2138,8899,5495,7358],()=>r(44469)),_N_E=e.O()}]);