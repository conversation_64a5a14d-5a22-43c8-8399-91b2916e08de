(()=>{var e={};e.id=1524,e.ids=[1524],e.modules={321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(37413);function r(){return(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-10 w-64 rounded mb-2"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 w-80 rounded"})]}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-10 w-32 rounded"})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Array.from({length:4}).map((e,t)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 w-20 rounded"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-10 w-full rounded"})]},t))}),(0,a.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-10 w-24 rounded"}),(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-10 w-20 rounded"})]})]}),(0,a.jsx)("div",{className:"card overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full text-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsx)("tr",{children:Array.from({length:11}).map((e,t)=>(0,a.jsx)("th",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 w-20 rounded"})},t))})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:Array.from({length:8}).map((e,t)=>(0,a.jsx)("tr",{className:"hover:bg-gray-50",children:Array.from({length:11}).map((e,t)=>(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 w-16 rounded"})},t))},t))})]})})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 w-32 rounded"}),(0,a.jsx)("div",{className:"flex space-x-2",children:Array.from({length:5}).map((e,t)=>(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-8 rounded"},t))})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6312:(e,t,s)=>{Promise.resolve().then(s.bind(s,85267))},9776:(e,t,s)=>{"use strict";s.d(t,{B0:()=>r,F6:()=>l});var a=s(60687);function r({className:e=""}){return(0,a.jsx)("div",{className:`glass rounded-2xl p-6 animate-pulse ${e}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function l({rows:e=5,columns:t=4}){return(0,a.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${t}, 1fr)`},children:Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded"},t))})}),Array.from({length:e}).map((e,s)=>(0,a.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,a.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${t}, 1fr)`},children:Array.from({length:t}).map((e,t)=>(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded"},t))})},s))]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40903:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["logs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,81076)),"C:\\RoKey App\\rokey-app\\src\\app\\logs\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,321)),"C:\\RoKey App\\rokey-app\\src\\app\\logs\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\logs\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/logs/page",pathname:"/logs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42760:(e,t,s)=>{Promise.resolve().then(s.bind(s,81076))},49579:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50515:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},55296:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},70149:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81076:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\logs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\logs\\page.tsx","default")},81836:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(43210);let r=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},85267:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(60687),r=s(43210),l=s(70149),n=s(55296);let i=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"}))});var o=s(73559),d=s(50515);let c=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))}),m=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var u=s(49579),p=s(61245),x=s(66524),g=s(81836);let h=({label:e,value:t})=>(0,a.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-700",children:e}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:null!=t&&""!==t?t:"N/A"})]}),y=({title:e,data:t})=>{let s;if(null==t)s="N/A";else if("string"==typeof t)s=t;else try{s=JSON.stringify(t,null,2)}catch(e){s="Invalid JSON data"}return(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-700 mb-1",children:e}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function f({log:e,onClose:t,apiConfigNameMap:s}){var r;if(!e)return null;let l=e.custom_api_config_id?s[e.custom_api_config_id]||"Unknown Model":"N/A";return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:t,children:(0,a.jsxs)("div",{className:"card max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Log Details (ID: ",e.id.substring(0,8),"...)"]}),(0,a.jsx)("button",{onClick:t,className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,a.jsx)(g.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,a.jsxs)("dl",{className:"divide-y divide-gray-200",children:[(0,a.jsx)(h,{label:"Timestamp",value:new Date(e.request_timestamp).toLocaleString()}),(0,a.jsx)(h,{label:"API Model Used",value:l}),(0,a.jsx)(h,{label:"Role Requested",value:e.role_requested}),(0,a.jsx)(h,{label:"Role Used",value:e.role_used}),(0,a.jsx)(h,{label:"Status",value:null===(r=e.status_code)?(0,a.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):r>=200&&r<300?(0,a.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",r,")"]}):r>=400?(0,a.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",r,")"]}):(0,a.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",r,")"]})}),(0,a.jsx)(h,{label:"LLM Provider",value:e.llm_provider_name}),(0,a.jsx)(h,{label:"LLM Model Name",value:e.llm_model_name}),(0,a.jsx)(h,{label:"LLM Latency",value:null!==e.llm_provider_latency_ms?`${e.llm_provider_latency_ms} ms`:"N/A"}),(0,a.jsx)(h,{label:"RoKey Latency",value:null!==e.processing_duration_ms?`${e.processing_duration_ms} ms`:"N/A"}),(0,a.jsx)(h,{label:"Input Tokens",value:null!==e.input_tokens?e.input_tokens:"N/A"}),(0,a.jsx)(h,{label:"Output Tokens",value:null!==e.output_tokens?e.output_tokens:"N/A"}),(0,a.jsx)(h,{label:"Cost",value:null!==e.cost?`$${e.cost.toFixed(6)}`:"N/A"}),(0,a.jsx)(h,{label:"Multimodal Request",value:e.is_multimodal?"Yes":"No"}),(0,a.jsx)(h,{label:"IP Address",value:e.ip_address}),e.user_id&&(0,a.jsx)(h,{label:"User ID",value:e.user_id}),e.error_message&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h,{label:"Error Message",value:e.error_message}),(0,a.jsx)(h,{label:"Error Source",value:e.error_source})]}),e.llm_provider_status_code&&(0,a.jsx)(h,{label:"LLM Provider Status",value:e.llm_provider_status_code})]}),e.request_payload_summary&&(0,a.jsx)(y,{title:"Request Payload Summary",data:e.request_payload_summary}),e.response_payload_summary&&(0,a.jsx)(y,{title:"Response Payload Summary",data:e.response_payload_summary}),e.error_details_zod&&(0,a.jsx)(y,{title:"Zod Validation Error Details",data:e.error_details_zod})]}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-200 text-right",children:(0,a.jsx)("button",{onClick:t,className:"btn-secondary",children:"Close"})})]})})}var v=s(9776);let j=e=>![/default_key/i,/attempt_\d+/i,/status_\d+/i,/failed/i,/success/i,/complexity_rr/i,/fallback_position/i,/^[a-f0-9-]{8,}/i,/_then_/i,/classification_/i,/no_prompt/i,/error/i].some(t=>t.test(e))&&/^[a-z_]+$/i.test(e)&&e.length>2&&e.length<50,b=e=>{if(!e)return{text:"N/A",type:"fallback"};if(j(e))return{text:k(e),type:"role",details:`Role-based routing: ${k(e)}`};if(e.includes("default_key")&&e.includes("success")){let t=e.match(/attempt_(\d+)/),s=t?parseInt(t[1]):1;return{text:1===s?"Default Key":`Default Key (Attempt ${s})`,type:"success",details:s>1?`Required ${s} attempts to succeed`:void 0}}if(e.includes("default_key")&&e.includes("failed")){let t=e.match(/attempt_(\d+)/),s=e.match(/status_(\w+)/),a=t?parseInt(t[1]):1,r=s?s[1]:"unknown";return{text:`Failed (Attempt ${a})`,type:"error",details:`Failed with status: ${r}`}}if(e.includes("default_all")&&e.includes("attempts_failed")){let t=e.match(/default_all_(\d+)_attempts/),s=t?parseInt(t[1]):0;return{text:`All Keys Failed (${s} attempts)`,type:"error",details:`Tried ${s} different API keys, all failed`}}if(e.includes("complexity_rr_clsf_")||e.includes("complexity_level_")){let t=e.match(/complexity_rr_clsf_(\d+)_used_lvl_(\d+)/);if(t){let e=t[1],s=t[2];return e===s?{text:`Complexity Level ${s}`,type:"success",details:`Classified and routed to complexity level ${s}`}:{text:`Complexity ${e}→${s}`,type:"success",details:`Classified as level ${e}, routed to available level ${s}`}}let s=e.match(/complexity_level_(\d+)/);if(s){let e=s[1];return{text:`Complexity Level ${e}`,type:"success",details:"Routed based on prompt complexity analysis"}}}if(e.includes("fallback_position_")){let t=e.match(/fallback_position_(\d+)/),s=t?parseInt(t[1]):0;return{text:`Fallback Key #${s+1}`,type:"success",details:`Used fallback key at position ${s+1}`}}if(e.includes("intelligent_role_")){let t=e.match(/intelligent_role_(.+)$/),s=t?t[1]:"unknown";return{text:`Smart: ${k(s)}`,type:"role",details:`AI detected role: ${k(s)}`}}return _(e)},_=e=>{let t=e.match(/complexity[_\s]*(\d+)/i);if(t){let e=t[1];return{text:`Complexity Level ${e}`,type:"success",details:`Extracted complexity level ${e} from routing pattern`}}let s=N(e);if(s)return{text:k(s),type:"role",details:`Extracted role: ${k(s)}`};let a=e.match(/fallback[_\s]*(\d+)/i);if(a){let e=parseInt(a[1]);return{text:`Fallback Key #${e+1}`,type:"success",details:`Extracted fallback position ${e+1}`}}let r=e.match(/attempt[_\s]*(\d+)/i);if(r){let t=parseInt(r[1]),s=e.toLowerCase().includes("success"),a=e.toLowerCase().includes("fail");if(s)return{text:1===t?"Default Key":`Default Key (Attempt ${t})`,type:"success",details:`Extracted success on attempt ${t}`};if(a)return{text:`Failed (Attempt ${t})`,type:"error",details:`Extracted failure on attempt ${t}`}}return{text:w(e),type:"fallback",details:`Raw routing pattern: ${e}`}},N=e=>{let t=e.match(/classified_as_([a-z_]+)_/i);if(t&&j(t[1]))return t[1];let s=e.match(/role_([a-z_]+)_/i);if(s&&j(s[1]))return s[1];let a=e.match(/fb_role_([a-z_]+)/i);return a&&j(a[1])?a[1]:null},w=e=>{let t=e.replace(/^default_key_[a-f0-9-]+_/i,"").replace(/_attempt_\d+$/i,"").replace(/_status_\w+$/i,"").replace(/_key_selected$/i,"").replace(/_then_.*$/i,"").replace(/^complexity_rr_/i,"").replace(/_no_.*$/i,"");return t&&t.length>2&&t.length<30&&j(t)?k(t):e.replace(/_/g," ").replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ").substring(0,30)+(e.length>30?"...":"")},k=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),A=e=>{switch(e){case"role":return"px-2 py-1 bg-blue-100 text-blue-800 rounded-lg text-xs font-medium";case"success":return"px-2 py-1 bg-green-100 text-green-800 rounded-lg text-xs font-medium";case"error":return"px-2 py-1 bg-red-100 text-red-800 rounded-lg text-xs font-medium";default:return"px-2 py-1 bg-orange-100 text-orange-800 rounded-lg text-xs font-medium"}},C=e=>e?({openai:"OpenAI",anthropic:"Anthropic",google:"Google",openrouter:"OpenRouter",deepseek:"DeepSeek",xai:"xAI"})[e.toLowerCase()]||e:"N/A",L=e=>e?e.replace(/^(gpt-|claude-|gemini-|meta-llama\/|deepseek-|grok-)/,"").replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"N/A",$=[{label:"Timestamp",field:"request_timestamp",defaultSortOrder:"desc"},{label:"API Model",field:"custom_api_config_id"},{label:"Role Used",field:"role_used"},{label:"Provider",field:"llm_provider_name"},{label:"LLM Model",field:"llm_model_name"},{label:"Status",field:"status_code"},{label:"Latency (LLM)",field:"llm_provider_latency_ms"},{label:"Latency (RoKey)",field:"processing_duration_ms"},{label:"Input Tokens",field:"input_tokens"},{label:"Output Tokens",field:"output_tokens"}];function E(){let[e,t]=(0,r.useState)([]),[s,g]=(0,r.useState)(null),[h,y]=(0,r.useState)(!0),[j,_]=(0,r.useState)(!0),[N,w]=(0,r.useState)(null),[k,E]=(0,r.useState)([]),S={startDate:"",endDate:"",customApiConfigId:"all",status:"all"},[M,P]=(0,r.useState)(S),[R,D]=(0,r.useState)(!1),[I,O]=(0,r.useState)({}),[q,Z]=(0,r.useState)(!1),[B,F]=(0,r.useState)(null),[K,z]=(0,r.useState)({sortBy:"request_timestamp",sortOrder:"desc"}),H=(0,r.useCallback)(async(e=1,s,a)=>{y(!0),w(null);try{let r={page:e.toString(),pageSize:"10",sortBy:a.sortBy,sortOrder:a.sortOrder};s.startDate&&(r.startDate=new Date(s.startDate).toISOString()),s.endDate&&(r.endDate=new Date(s.endDate).toISOString()),"all"!==s.customApiConfigId&&(r.customApiConfigId=s.customApiConfigId),"all"!==s.status&&(r.status=s.status);let l=await fetch(`/api/logs?${new URLSearchParams(r).toString()}`);if(!l.ok){let e=await l.json();throw Error(e.error||e.details||"Failed to fetch logs")}let n=await l.json();t(n.logs||[]),g(n.pagination||null)}catch(e){w(e.message),t([]),g(null)}finally{y(!1)}},[]),V=e=>{P(t=>({...t,[e.target.name]:e.target.value}))},T=e=>{e>0&&(!s||e<=s.totalPages)&&H(e,M,K)},U=e=>{let t=K.sortBy===e&&"asc"===K.sortOrder?"desc":"asc",s={sortBy:e,sortOrder:t};z(s),H(1,M,s)},W=e=>{F(e),Z(!0)},G=e=>null===e?"bg-gray-600 text-gray-100":e>=200&&e<300?"bg-green-600 text-green-100":e>=400?"bg-red-600 text-red-100":"bg-yellow-500 text-yellow-100",J=({column:e})=>{let t=K.sortBy===e.field;return(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider",children:(0,a.jsxs)("button",{onClick:()=>U(e.field),className:"flex items-center space-x-2 hover:text-gray-900 transition-colors duration-200 group",children:[(0,a.jsx)("span",{children:e.label}),t?"asc"===K.sortOrder?(0,a.jsx)(l.A,{className:"h-4 w-4 text-orange-500"}):(0,a.jsx)(n.A,{className:"h-4 w-4 text-orange-500"}):(0,a.jsx)(i,{className:"h-4 w-4 text-gray-400 group-hover:text-gray-600"})]})})};return(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"\uD83D\uDCCA Request Logs"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Monitor and analyze your API request history"})]}),(0,a.jsxs)("button",{onClick:()=>D(!R),className:R?"btn-primary":"btn-secondary",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),R?"Hide Filters":"Show Filters"]})]}),N&&(0,a.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("p",{className:"text-red-800",children:N})]})}),R&&(0,a.jsxs)("div",{className:"card p-6 animate-scale-in",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Filter Logs"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Narrow down your search results"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Real-time updates"})]})]})}),(0,a.jsxs)("form",{onSubmit:e=>{e?.preventDefault(),H(1,M,K)},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(c,{className:"h-4 w-4 inline mr-1"}),"Start Date"]}),(0,a.jsx)("input",{type:"date",name:"startDate",value:M.startDate,onChange:V,className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(c,{className:"h-4 w-4 inline mr-1"}),"End Date"]}),(0,a.jsx)("input",{type:"date",name:"endDate",value:M.endDate,onChange:V,className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Model"}),(0,a.jsxs)("select",{name:"customApiConfigId",value:M.customApiConfigId,onChange:V,disabled:j,className:"form-select",children:[(0,a.jsx)("option",{value:"all",children:"All Models"}),k.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{name:"status",value:M.status,onChange:V,className:"form-select",children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"success",children:"Success"}),(0,a.jsx)("option",{value:"error",children:"Error"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)("button",{type:"submit",className:"btn-primary flex-1",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-2"}),"Apply Filters"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{P(S);let e={sortBy:"request_timestamp",sortOrder:"desc"};z(e),H(1,S,e)},className:"btn-secondary flex-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Reset Filters"]})]})]})]}),h&&(0,a.jsx)(v.F6,{rows:8,columns:11}),!h&&!e.length&&!N&&(0,a.jsx)("div",{className:"card text-center py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-orange-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No Logs Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"No request logs match your criteria. Once you make requests to the unified API, they will appear here."})]})}),!h&&e.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"card overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full text-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[$.map(e=>(0,a.jsx)(J,{column:e},e.field)),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider",children:"Multimodal"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:e.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-200 animate-slide-in",style:{animationDelay:`${50*t}ms`},children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{children:new Date(e.request_timestamp).toLocaleString()})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900",children:(0,a.jsx)("div",{className:"font-medium",children:e.custom_api_config_id?I[e.custom_api_config_id]||e.custom_api_config_id.substring(0,8)+"...":"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900",children:(()=>{let t=b(e.role_used);return(0,a.jsx)("span",{className:A(t.type),children:t.text})})()}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900",children:(0,a.jsx)("span",{className:"font-medium",children:C(e.llm_provider_name)})}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900 truncate max-w-xs",title:L(e.llm_model_name),children:(0,a.jsx)("span",{className:"font-medium",children:L(e.llm_model_name)})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${G(e.status_code)}`,children:e.status_code||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.llm_provider_latency_ms?`${e.llm_provider_latency_ms} ms`:"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.processing_duration_ms?`${e.processing_duration_ms} ms`:"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.input_tokens?e.input_tokens.toLocaleString():"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.output_tokens?e.output_tokens.toLocaleString():"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-center",children:e.is_multimodal?(0,a.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full inline-block"}):(0,a.jsx)("span",{className:"w-2 h-2 bg-gray-400 rounded-full inline-block"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("button",{onClick:()=>W(e),className:"p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})})]},e.id))})]})})}),s&&s.totalPages>1&&(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(s.currentPage-1)*s.pageSize+1}),e.length>0?` - ${Math.min(s.currentPage*s.pageSize,s.totalCount)}`:""," ","of ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s.totalCount})," logs"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>T(s.currentPage-1),disabled:s.currentPage<=1||h,className:"btn-secondary text-sm px-3 py-1",children:"Previous"}),(0,a.jsxs)("span",{className:"px-3 py-1 text-sm text-gray-600",children:["Page ",s.currentPage," of ",s.totalPages]}),(0,a.jsx)("button",{onClick:()=>T(s.currentPage+1),disabled:s.currentPage>=s.totalPages||h,className:"btn-secondary text-sm px-3 py-1",children:"Next"})]})]})})]}),q&&B&&(0,a.jsx)(f,{log:B,onClose:()=>{Z(!1),F(null)},apiConfigNameMap:I})]})}},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,8153,1658,8947],()=>s(40903));module.exports=a})();