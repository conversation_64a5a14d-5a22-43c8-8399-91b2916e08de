(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[807],{16809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(95155),i=r(12115),n=r(6874),a=r.n(n),o=r(5279),l=r(64274);let c=i.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?i.createElement("title",{id:s},r):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))});var d=r(64353),x=r(5246),g=r(8246),m=r(53951);function u(){let[e,t]=(0,i.useState)([]),[r,n]=(0,i.useState)(!0),[u,h]=(0,i.useState)(null),{createHoverPrefetch:b}=(0,m.c)();return(0,i.useEffect)(()=>{!async function(){n(!0),h(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations")}let r=await e.json();t(r)}catch(e){h(e.message),t([])}finally{n(!1)}}()},[]),(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-h1 text-gray-900 mb-2",children:"Advanced Routing Setup"}),(0,s.jsx)("p",{className:"text-body text-gray-600 max-w-2xl",children:"Configure intelligent routing strategies for your API configurations with enterprise-grade precision"})]}),(0,s.jsxs)("div",{children:[r&&(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin mx-auto mb-3"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading configurations..."})]})}),u&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-3 h-3 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-red-800 text-sm",children:"Error Loading Configurations"}),(0,s.jsx)("p",{className:"text-red-700 text-xs mt-0.5",children:u})]})]})}),!r&&!u&&0===e.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"card p-8 max-w-sm mx-auto",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-full flex items-center justify-center mx-auto mb-4 border border-orange-100",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-orange-500"})}),(0,s.jsx)("h3",{className:"text-h4 text-gray-900 mb-2",children:"No Configurations Found"}),(0,s.jsx)("p",{className:"text-body-sm text-gray-600 mb-4 leading-relaxed",children:"Create your first Custom API Configuration to start setting up intelligent routing strategies"}),(0,s.jsxs)(a(),{href:"/my-models",className:"btn-primary inline-flex items-center text-sm",children:[(0,s.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Create Configuration"]})]})}),!r&&!u&&e.length>0&&(0,s.jsx)("div",{className:"grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:e.map((e,t)=>{let r=[{bg:"bg-gradient-to-br from-pink-500 to-rose-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-blue-500 to-blue-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-emerald-500 to-green-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-amber-500 to-orange-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-purple-500 to-violet-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-cyan-500 to-teal-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-indigo-500 to-blue-700",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-red-500 to-pink-600",icon:"text-white",text:"text-white"}],i=r[t%r.length],n=(e=>{switch(e){case"intelligent_role":return l.A;case"complexity_round_robin":return d.A;case"strict_fallback":return g.A;case"auto_optimal":return o.A;default:return x.A}})(e.routing_strategy||"none");return(0,s.jsx)(a(),{href:"/routing-setup/".concat(e.id,"?from=routing-setup"),className:"group block transition-all duration-300 hover:scale-[1.02] hover:shadow-xl",...b(e.id),children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden h-80",children:[(0,s.jsxs)("div",{className:"".concat(i.bg," p-8 h-48 flex flex-col items-center justify-center relative"),children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,s.jsx)(n,{className:"w-10 h-10 ".concat(i.icon)})}),(0,s.jsx)("div",{className:"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full",children:(0,s.jsx)("span",{className:"text-sm font-bold ".concat(i.text),children:"none"!==e.routing_strategy&&e.routing_strategy?"intelligent_role"===e.routing_strategy?"Smart Role":"complexity_round_robin"===e.routing_strategy?"Complexity":"strict_fallback"===e.routing_strategy?"Fallback":e.routing_strategy:"Default"})}),(0,s.jsx)(c,{className:"w-6 h-6 ".concat(i.text," absolute top-4 right-4 group-hover:translate-x-1 transition-transform duration-300")})]}),(0,s.jsxs)("div",{className:"p-6 h-32 flex flex-col justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-black group-hover:text-gray-800 transition-colors duration-200 line-clamp-2 leading-tight",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Advanced routing configuration with intelligent strategies"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-xs text-gray-500 font-medium",children:["Created ",new Date(e.created_at).toLocaleDateString()]}),(0,s.jsx)("button",{className:"px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors duration-200",children:"Configure"})]})]})]})},e.id)})})]})]})}},58585:(e,t,r)=>{Promise.resolve().then(r.bind(r,16809))}},e=>{var t=t=>e(e.s=t);e.O(0,[5738,1486,2662,8669,8848,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(58585)),_N_E=e.O()}]);