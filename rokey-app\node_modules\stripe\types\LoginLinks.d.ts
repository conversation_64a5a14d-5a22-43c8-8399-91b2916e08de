// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    /**
     * Login Links are single-use URLs for a connected account to access the Express Dashboard. The connected account's [account.controller.stripe_dashboard.type](https://stripe.com/api/accounts/object#account_object-controller-stripe_dashboard-type) must be `express` to have access to the Express Dashboard.
     */
    interface LoginLink {
      /**
       * String representing the object's type. Objects of the same type share the same value.
       */
      object: 'login_link';

      /**
       * Time at which the object was created. Measured in seconds since the Unix epoch.
       */
      created: number;

      /**
       * The URL for the login link.
       */
      url: string;
    }
  }
}
