{"/_not-found/page": "app/_not-found/page.js", "/api/analytics/summary/route": "app/api/analytics/summary/route.js", "/api/chat/conversations/route": "app/api/chat/conversations/route.js", "/api/activity/route": "app/api/activity/route.js", "/api/chat/messages/delete-after-timestamp/route": "app/api/chat/messages/delete-after-timestamp/route.js", "/api/chat/messages/route": "app/api/chat/messages/route.js", "/api/chat/messages/update-by-timestamp/route": "app/api/chat/messages/update-by-timestamp/route.js", "/api/custom-configs/[configId]/default-chat-key/route": "app/api/custom-configs/[configId]/default-chat-key/route.js", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "app/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route.js", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "app/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route.js", "/api/custom-configs/[configId]/route": "app/api/custom-configs/[configId]/route.js", "/api/custom-configs/[configId]/routing/route": "app/api/custom-configs/[configId]/routing/route.js", "/api/custom-configs/route": "app/api/custom-configs/route.js", "/api/keys/[apiKeyId]/roles/[roleName]/route": "app/api/keys/[apiKeyId]/roles/[roleName]/route.js", "/api/keys/[apiKeyId]/roles/route": "app/api/keys/[apiKeyId]/roles/route.js", "/api/keys/[apiKeyId]/route": "app/api/keys/[apiKeyId]/route.js", "/api/keys/route": "app/api/keys/route.js", "/api/logs/route": "app/api/logs/route.js", "/api/orchestration/process-step/route": "app/api/orchestration/process-step/route.js", "/api/orchestration/start/route": "app/api/orchestration/start/route.js", "/api/orchestration/status/[executionId]/route": "app/api/orchestration/status/[executionId]/route.js", "/api/orchestration/stream/[executionId]/route": "app/api/orchestration/stream/[executionId]/route.js", "/api/orchestration/synthesis-fallback/[executionId]/route": "app/api/orchestration/synthesis-fallback/[executionId]/route.js", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "app/api/orchestration/synthesis-stream-direct/[executionId]/route.js", "/api/orchestration/synthesis-stream/[executionId]/route": "app/api/orchestration/synthesis-stream/[executionId]/route.js", "/api/orchestration/synthesis-test-stream/[executionId]/route": "app/api/orchestration/synthesis-test-stream/[executionId]/route.js", "/api/playground/route": "app/api/playground/route.js", "/api/providers/list-models/route": "app/api/providers/list-models/route.js", "/api/system-status/route": "app/api/system-status/route.js", "/api/training/jobs/route": "app/api/training/jobs/route.js", "/api/training/jobs/upsert/route": "app/api/training/jobs/upsert/route.js", "/api/user/custom-roles/[customRoleId]/route": "app/api/user/custom-roles/[customRoleId]/route.js", "/api/user/custom-roles/route": "app/api/user/custom-roles/route.js", "/api/v1/chat/completions/route": "app/api/v1/chat/completions/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/about/page": "app/about/page.js", "/add-keys/page": "app/add-keys/page.js", "/analytics/page": "app/analytics/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/dashboard/page": "app/dashboard/page.js", "/features/page": "app/features/page.js", "/logs/page": "app/logs/page.js", "/my-models/page": "app/my-models/page.js", "/my-models/[configId]/page": "app/my-models/[configId]/page.js", "/page": "app/page.js", "/playground/page": "app/playground/page.js", "/pricing/page": "app/pricing/page.js", "/routing-setup/[configId]/page": "app/routing-setup/[configId]/page.js", "/routing-setup/page": "app/routing-setup/page.js", "/training/page": "app/training/page.js"}