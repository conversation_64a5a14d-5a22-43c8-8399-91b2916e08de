(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7545],{32183:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(95155),a=t(12115),n=t(35695),i=t(6874),l=t.n(i),o=t(26784),d=t(32461),c=t(15713),m=t(64274),x=t(6865),h=t(64353),u=t(37186);let g=a.forwardRef(function(e,s){let{title:t,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},n),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))});var p=t(53951),f=t(14446);let j=[{id:"none",name:"Default Behavior",shortDescription:"Automatic load balancing",description:"RoKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:u.A},{id:"intelligent_role",name:"Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"RoKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:m.A},{id:"complexity_round_robin",name:"Complexity-Based Round-Robin",shortDescription:"Route by prompt complexity",description:"RoKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:h.A},{id:"strict_fallback",name:"Strict Fallback",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RoKey will try them in sequence until one succeeds.",icon:g}];function y(e){let{apiKey:s,index:t,onMoveUp:a,onMoveDown:n}=e;return(0,r.jsx)("li",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-semibold text-orange-600",children:t+1})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:s.label}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:[s.provider," - ",s.predefined_model_id]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[a&&(0,r.jsx)("button",{onClick:a,className:"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200",title:"Move up",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})}),n&&(0,r.jsx)("button",{onClick:n,className:"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200",title:"Move down",children:(0,r.jsx)(o.A,{className:"w-4 h-4"})})]})]})})}function b(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),t=(0,n.useSearchParams)(),i=e.configId,{getCachedData:o,isCached:c}=(0,p.c)(),b=()=>{let e=t.get("from");if("routing-setup"===e)return"/routing-setup";if("model-config"===e)return"/my-models/".concat(i);{let e=document.referrer,s=window.location.host;if(e&&e.includes(s))try{let s=new URL(e).pathname;if("/routing-setup"===s)return"/routing-setup";if(s==="/my-models/".concat(i)||"/my-models"===s)return"/my-models/".concat(i);s.startsWith("/my-models/")&&s.includes("/routing-setup")}catch(e){}}return"/my-models/".concat(i)},[v,N]=(0,a.useState)(null),[w,k]=(0,a.useState)(!0),[_,S]=(0,a.useState)(null),[A,C]=(0,a.useState)(null),[L,R]=(0,a.useState)(!1),[M,P]=(0,a.useState)("none"),[B,F]=(0,a.useState)({}),[E,I]=(0,a.useState)([]),[K,D]=(0,a.useState)(!1),[H,W]=(0,a.useState)([]),[q,O]=(0,a.useState)(null),[T,Z]=(0,a.useState)({}),[z,U]=(0,a.useState)([]),[G,J]=(0,a.useState)(!1),[V,Q]=(0,a.useState)(!1),[X,Y]=(0,a.useState)(null),[$,ee]=(0,a.useState)(null),es=(0,a.useCallback)(async()=>{if(!i){S("Configuration ID is missing."),k(!1);return}let e=o(i);if(e&&e.configDetails&&e.apiKeys){var s;N(e.configDetails),I(e.apiKeys);let t=e.routingStrategy||"none";if(P(t),F(e.routingParams||{}),"strict_fallback"===t&&(null==(s=e.routingParams)?void 0:s.ordered_api_key_ids)){let s=e.routingParams.ordered_api_key_ids;W([...s.map(s=>e.apiKeys.find(e=>e.id===s)).filter(Boolean),...e.apiKeys.filter(e=>!s.includes(e.id))])}else W([...e.apiKeys]);k(!1),D(!1);return}c(i)||R(!0),k(!0),D(!0),S(null),C(null);try{let e=await fetch("/api/custom-configs/".concat(i));if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch configuration")}let s=await e.json();N(s);let t=s.routing_strategy||"none";P(t);let r=s.routing_strategy_params||{};F(r);let a=await fetch("/api/keys?custom_config_id=".concat(i));if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to fetch API keys for this configuration")}let n=await a.json();if(I(n),"strict_fallback"===t&&r.ordered_api_key_ids){let e=r.ordered_api_key_ids,s=e.map(e=>n.find(s=>s.id===e)).filter(Boolean),t=n.filter(s=>!e.includes(s.id));W([...s,...t])}else W([...n])}catch(e){S("Error loading data: ".concat(e.message)),N(null),I([])}finally{k(!1),D(!1),R(!1)}},[i,o,c]);(0,a.useEffect)(()=>{es()},[es]);let et=(0,a.useCallback)(async e=>{if(i&&e){J(!0),Y(null),ee(null);try{let s=await fetch("/api/custom-configs/".concat(i,"/keys/").concat(e,"/complexity-assignments"));if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to fetch complexity assignments")}let t=await s.json();Z(s=>({...s,[e]:t})),U(t)}catch(e){Y("Error fetching assignments for key: ".concat(e.message)),U([])}finally{J(!1)}}},[i]);(0,a.useEffect)(()=>{q?et(q):(U([]),Y(null))},[q,et]);let er=(e,s)=>{U(t=>s?[...t,e].sort((e,s)=>e-s):t.filter(s=>s!==e))},ea=(0,a.useCallback)(async()=>{if(!i||!q)return void Y("No API key selected to save assignments for.");Q(!0),Y(null),ee(null);try{let e=await fetch("/api/custom-configs/".concat(i,"/keys/").concat(q,"/complexity-assignments"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({complexity_levels:z})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save complexity assignments")}let s=await e.json();Z(e=>({...e,[q]:[...z]})),ee(s.message||"Complexity assignments saved successfully!")}catch(e){Y("Error saving assignments: ".concat(e.message))}finally{Q(!1)}},[i,q,z]),en=(e,s)=>{let t=[...H],r=t[e];"up"===s&&e>0?(t.splice(e,1),t.splice(e-1,0,r)):"down"===s&&e<t.length-1&&(t.splice(e,1),t.splice(e+1,0,r)),W(t),F({ordered_api_key_ids:t.map(e=>e.id)})},ei=async e=>{if(e.preventDefault(),!i||!v)return void S("Configuration details not loaded.");k(!0),S(null),C(null);let s=B;"strict_fallback"===M&&(s={ordered_api_key_ids:H.map(e=>e.id)});try{let e=await fetch("/api/custom-configs/".concat(i,"/routing"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({routing_strategy:M,routing_strategy_params:s})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save routing settings")}let t=await e.json();C(t.message||"Routing settings saved successfully!"),N(e=>e?{...e,routing_strategy:M,routing_strategy_params:s}:null),F(s)}catch(e){S("Error saving settings: ".concat(e.message))}finally{k(!1)}},el=()=>{var e;return"complexity_round_robin"!==M?null:(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Complexity-Based Key Assignments"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity."}),K&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading API keys..."})]}),!K&&0===E.length&&(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"No API keys found for this configuration. Please add API keys first on the model configuration page."})}),E.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"apiKeyForComplexity",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Key to Assign Complexities:"}),(0,r.jsxs)("select",{id:"apiKeyForComplexity",value:q||"",onChange:e=>O(e.target.value||null),className:"form-select max-w-md",children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"-- Select an API Key --"}),E.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.label," (",e.provider," - ",e.predefined_model_id,")"]},e.id))]})]}),q&&(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-gray-900 mb-4",children:["Assign Complexity Levels for: ",(0,r.jsx)("span",{className:"text-orange-600",children:null==(e=E.find(e=>e.id===q))?void 0:e.label})]}),G&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading current assignments..."})]}),X&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:X})}),$&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:$})}),!G&&(0,r.jsx)("div",{className:"space-y-3 mb-6",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200",children:[(0,r.jsx)("input",{type:"checkbox",checked:z.includes(e),onChange:s=>er(e,s.target.checked),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["Complexity Level ",e]})]},e))}),(0,r.jsx)("button",{onClick:ea,disabled:V||G,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:V?"Saving Assignments...":"Save Assignments for this Key"})]})]})};return L&&!c(i)?(0,r.jsx)(f.Ay,{}):w&&!v?(0,r.jsx)(f.CE,{}):!_||v||w?(0,r.jsx)("div",{className:"min-h-screen bg-cream",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("button",{onClick:()=>{if(window.history.length>1){b();let e=document.referrer,t=window.location.host;if(e&&e.includes(t))return void s.back()}s.push(b())},className:"btn-secondary inline-flex items-center text-sm",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),(()=>{let e=t.get("from");if("routing-setup"===e)return"Back to Routing Setup";if("model-config"===e)return"Back to Configuration";{let e=document.referrer,s=window.location.host;if(e&&e.includes(s))try{let s=new URL(e).pathname;if("/routing-setup"===s)return"Back to Routing Setup";s==="/my-models/".concat(i)||"/my-models"===s||s.startsWith("/my-models/")}catch(e){}}return"Back to Configuration"})()]})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h1",{className:"text-h1 text-gray-900",children:"Advanced Routing Setup"}),v&&(0,r.jsxs)("p",{className:"text-body-sm text-gray-600 mt-1",children:["Configuration: ",(0,r.jsx)("span",{className:"text-orange-600 font-semibold",children:v.name})]})]})]})}),_&&!A&&(0,r.jsx)("div",{className:"card border-red-200 bg-red-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-red-800",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-700 mt-1",children:_})]})]})}),A&&(0,r.jsx)("div",{className:"card border-green-200 bg-green-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-800",children:"Settings Saved"}),(0,r.jsx)("p",{className:"text-green-700 mt-1",children:A})]})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6 sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Routing Strategy"}),(0,r.jsx)("div",{className:"space-y-3",children:j.map(e=>{let s=e.icon,t=M===e.id;return(0,r.jsx)("button",{onClick:()=>{if(P(e.id),"strict_fallback"===e.id){let e=B.ordered_api_key_ids;e&&Array.isArray(e)?W([...e.map(e=>E.find(s=>s.id===e)).filter(Boolean),...E.filter(s=>!e.includes(s.id))]):W([...E]),F({ordered_api_key_ids:H.map(e=>e.id)})}else F({}),W([...E]);O(null),U([]),Y(null),ee(null)},disabled:w,className:"w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group ".concat(t?"border-orange-500 bg-orange-50 shadow-lg transform scale-[1.02]":"border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25 hover:shadow-md"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-lg transition-colors duration-300 ".concat(t?"bg-orange-100 text-orange-600":"bg-gray-100 text-gray-600 group-hover:bg-orange-100 group-hover:text-orange-600"),children:(0,r.jsx)(s,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm transition-colors duration-300 ".concat(t?"text-orange-900":"text-gray-900"),children:e.name}),t&&(0,r.jsx)(x.A,{className:"w-4 h-4 text-orange-600 animate-in fade-in duration-300"})]}),(0,r.jsx)("p",{className:"text-xs leading-relaxed transition-colors duration-300 ".concat(t?"text-orange-700":"text-gray-600"),children:e.shortDescription})]})]})},e.id)})})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("form",{onSubmit:ei,children:(0,r.jsx)("div",{className:"card p-8 min-h-[600px]",children:(0,r.jsx)("div",{className:"animate-in fade-in slide-in-from-right-4 duration-500",children:(()=>{let e=j.find(e=>e.id===M);return"none"===M?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(u.A,{className:"w-10 h-10 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Default Behavior"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-md mx-auto leading-relaxed",children:null==e?void 0:e.description}),(0,r.jsx)("div",{className:"mt-8 p-4 bg-green-50 border border-green-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-green-800 font-medium",children:"No additional setup required"})]})}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"intelligent_role"===M?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Intelligent Role Routing"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"1"})}),(0,r.jsx)("p",{children:"System analyzes your prompt to understand the main task"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"2"})}),(0,r.jsx)("p",{children:"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"3"})}),(0,r.jsx)("p",{children:"Routes to assigned API key or falls back to 'Default General Chat Model'"})]})]})]}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-800 leading-relaxed",children:"No additional setup required. Future enhancements may allow further customization."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"strict_fallback"===M?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(g,{className:"w-7 h-7 mr-3 text-orange-600"}),"Strict Fallback Configuration"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),K&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-orange-600/20 border-t-orange-600 rounded-full animate-spin"}),(0,r.jsx)("p",{className:"text-gray-600 ml-3",children:"Loading API keys..."})]}),!K&&0===E.length&&(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-yellow-900 mb-2",children:"No API Keys Found"}),(0,r.jsx)("p",{className:"text-yellow-800 leading-relaxed",children:"Please add API keys on the main configuration page to set up fallback order."})]}),!K&&E.length>0&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on."})]})}),(0,r.jsx)("ul",{className:"space-y-3",children:H.map((e,s)=>(0,r.jsx)(y,{apiKey:e,index:s,onMoveUp:s>0?()=>en(s,"up"):void 0,onMoveDown:s<H.length-1?()=>en(s,"down"):void 0},e.id))})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w||0===E.length,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"complexity_round_robin"===M?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(h.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Complexity-Based Round-Robin"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),el(),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:w,children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):null})()})})})})]})})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Routing Setup Error"}),(0,r.jsxs)("div",{className:"card border-red-200 bg-red-50 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,r.jsx)("p",{className:"text-red-800",children:_})]}),(0,r.jsx)(l(),{href:"/my-models",className:"mt-4 btn-primary inline-block",children:"Back to My Models"})]})]})}},58496:(e,s,t)=>{Promise.resolve().then(t.bind(t,32183))}},e=>{var s=s=>e(e.s=s);e.O(0,[274,5738,1486,2662,8669,8848,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(58496)),_N_E=e.O()}]);