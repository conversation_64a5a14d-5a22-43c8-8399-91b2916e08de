"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2662],{175:(t,e,i)=>{i.d(e,{Q:()=>o});let r=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function o(t){if("string"!=typeof t||t.includes("-"));else if(r.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}},2735:(t,e,i)=>{function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,o){if("function"==typeof e){let[n,s]=r(o);e=e(void 0!==i?i:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=r(o);e=e(void 0!==i?i:t.custom,n,s)}return e}i.d(e,{a:()=>o})},6642:(t,e,i)=>{i.d(e,{B:()=>o});let r={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},o={};for(let t in r)o[t]={isEnabled:e=>r[t].some(t=>!!e[t])}},9480:(t,e,i)=>{i.d(e,{Y:()=>o});var r=i(6642);function o(t){for(let e in t)r.B[e]={...r.B[e],...t[e]}}},13513:(t,e,i)=>{i.d(e,{K:()=>n});var r=i(81786),o=i(40956);class n extends o.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(e in t){let i=t[e];if("string"==typeof i||"number"==typeof i)return i}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return(0,r.ge)()}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}},14060:(t,e,i)=>{function r(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}i.d(e,{I:()=>r}),i(21448)},18070:(t,e,i)=>{i.d(e,{Y:()=>n});var r=i(21448);let o=(t,e)=>t.depth-e.depth;class n{constructor(){this.children=[],this.isDirty=!1}add(t){(0,r.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,r.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(o),this.isDirty=!1,this.children.forEach(t)}}},18802:(t,e,i)=>{i.d(e,{U:()=>s});var r=i(43891),o=i(5910),n=i(20419);function s(t,e){let{transitionEnd:i={},transition:s={},...a}=(0,n.K)(t,e)||{};for(let e in a={...a,...i}){var l;let i=(l=a[e],(0,o.p)(l)?l[l.length-1]||0:l);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,r.OQ)(i))}}},19253:(t,e,i)=>{i.d(e,{O:()=>a,e:()=>s});var r=i(6340),o=i(65305),n=i(98312);function s(t){return(0,r.N)(t.animate)||n._.some(e=>(0,o.w)(t[e]))}function a(t){return!!(s(t)||t.variants)}},19394:(t,e,i)=>{i.d(e,{o:()=>r});let r=Symbol.for("motionComponentSymbol")},20419:(t,e,i)=>{i.d(e,{K:()=>o});var r=i(2735);function o(t,e,i){let o=t.getProps();return(0,r.a)(o,e,void 0!==i?i:o.custom,t)}},20600:(t,e,i)=>{i.d(e,{e:()=>r});function r(t,{style:e,vars:i},r,o){for(let n in Object.assign(t.style,e,o&&o.getProjectionStyles(r)),i)t.style.setProperty(n,i[n])}},20637:(t,e,i)=>{i.d(e,{$:()=>n,H:()=>o});var r=i(43891);let o={};function n(t){for(let e in t)o[e]=t[e],(0,r.j4)(e)&&(o[e].isCSSVariable=!0)}},21014:(t,e,i)=>{i.d(e,{Z:()=>y}),i(21448);var r=i(90869),o=i(25214),n=i(51508),s=i(2999),a=i(24132),l=i(68972),h=i(6642),u=i(9480),d=i(19394),c=i(33991);!function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var p=i(43891),m=i(31788),f=i(80845),v=i(70797),g=i(97494);function y(t){var e,i;let{preloadedFeatures:y,createVisualElement:x,useRender:T,useVisualState:O,Component:D}=t;function w(t,e){var i,u,d;let y,w={...Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(n.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(r.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:S}=w,P=(0,a.z)(t),E=O(t,S);if(!S&&l.B){u=0,d=0,Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(o.Y).strict;let t=function(t){let{drag:e,layout:i}=h.B;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(w);y=t.MeasureLayout,P.visualElement=function(t,e,i,r,a){let{visualElement:l}=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(s.A),h=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(o.Y),u=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(f.t),d=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(n.Q).reducedMotion,y=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(null);r=r||h.renderer,!y.current&&r&&(y.current=r(t,{visualState:e,parent:l,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:d}));let x=y.current,T=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(v.N);x&&!x.projection&&a&&("html"===x.type||"svg"===x.type)&&function(t,e,i,r){let{layoutId:o,layout:n,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:o,layout:n,alwaysMeasureLayout:!!s||a&&(0,c.X)(a),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:r,crossfade:u,layoutScroll:l,layoutRoot:h})}(y.current,i,a,T);let O=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(!1);Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{x&&O.current&&x.update(i,u)});let D=i[m.n],w=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(!!D&&!window.MotionHandoffIsComplete?.(D)&&window.MotionHasOptimisedAnimation?.(D));return(0,g.E)(()=>{x&&(O.current=!0,window.MotionIsMounted=!0,x.updateFeatures(),p.k2.render(x.render),w.current&&x.animationState&&x.animationState.animateChanges())}),Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{x&&(!w.current&&x.animationState&&x.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(D)}),w.current=!1))}),x}(D,E,w,x,t.ProjectionNode)}return Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(s.A.Provider,{value:P,children:[y&&P.visualElement?Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(y,{visualElement:P.visualElement,...w}):null,T(D,t,(i=P.visualElement,Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(t=>{t&&E.onMount&&E.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):(0,c.X)(e)&&(e.current=t))},[i])),E,S,P.visualElement)]})}y&&(0,u.Y)(y),w.displayName="motion.".concat("string"==typeof D?D:"create(".concat(null!=(i=null!=(e=D.displayName)?e:D.name)?i:"",")"));let S=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(w);return S[d.o]=D,S}!function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}(),!function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}()},22397:(t,e,i)=>{i.d(e,{b:()=>n});var r=i(43891),o=i(40956);class n extends o.B{constructor(){super(...arguments),this.KeyframeResolver=r.KN}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,r.SS)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}},29203:(t,e,i)=>{i.d(e,{O:()=>n});var r=i(43891),o=i(54577);function n(t,e,i){let{style:n,vars:s,transformOrigin:a}=t,l=!1,h=!1;for(let t in e){let i=e[t];if(r.fu.has(t)){l=!0;continue}if((0,r.j4)(t)){s[t]=i;continue}{let e=(0,r.eK)(i,r.Wh[t]);t.startsWith("origin")?(h=!0,a[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=(0,o.d)(e,t.transform,i):n.transform&&(n.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;n.transformOrigin=`${t} ${e} ${i}`}}},29901:(t,e,i)=>{i.d(e,{w:()=>r});let r={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},33055:(t,e,i)=>{i.d(e,{z:()=>n});var r=i(43891),o=i(20637);function n(t,{layout:e,layoutId:i}){return r.fu.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!o.H[t]||"opacity"===t)}},33757:(t,e,i)=>{i.d(e,{L:()=>s,m:()=>n});var r=i(78588),o=i(96147);function n(t,e){return(0,r.FY)((0,r.bS)(t.getBoundingClientRect(),e))}function s(t,e,i){let r=n(t,i),{scroll:s}=e;return s&&((0,o.Ql)(r.x,s.offset.x),(0,o.Ql)(r.y,s.offset.y)),r}},34527:(t,e,i)=>{i.d(e,{x:()=>n});var r=i(43891),o=i(60990);function n(t,e,i){let n=(0,o.x)(t,e,i);for(let i in t)((0,r.SS)(t[i])||(0,r.SS)(e[i]))&&(n[-1!==r.Us.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}},36324:(t,e,i)=>{i.d(e,{n:()=>p});var r=i(19726),o=i(19624),n=i(49441),s=i(52290);let a=new WeakMap,l=new WeakMap,h=t=>{let e=a.get(t.target);e&&e(t)},u=t=>{t.forEach(h)},d={some:0,all:1};class c extends s.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:o}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:d[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;l.has(i)||l.set(i,{});let r=l.get(i),o=JSON.stringify(e);return r[o]||(r[o]=new IntersectionObserver(u,{root:t,...e})),r[o]}(e);return a.set(t,i),r.observe(t),()=>{a.delete(t),r.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,o&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=e?i:r;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let p={inView:{Feature:c},tap:{Feature:n.H},focus:{Feature:o.c},hover:{Feature:r.e}}},36545:(t,e,i)=>{i.d(e,{P:()=>d});var r=i(14060),o=i(51563),n=i(44420),s=i(36324),a=i(53292),l=i(54160),h=i(59003);let u=(0,l.C)({...o.W,...s.n,...n.$,...a.Z},h.J),d=(0,r.I)(u)},40956:(t,e,i)=>{i.d(e,{B:()=>p});var r=i(43891),o=i(21448),n=i(6642),s=i(81786),a=i(42843),l=i(50408),h=i(65511),u=i(19253),d=i(2735);let c=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class p{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:o,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=r.hP,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=r.kB.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,r.Gt.render(this.render,!1,!0))};let{latestValues:l,renderState:h}=s;this.latestValues=l,this.baseTarget={...l},this.initialValues=e.initial?{...l}:{},this.renderState=h,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=(0,u.e)(e),this.isVariantNode=(0,u.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:d,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==l[t]&&(0,r.SS)(e)&&e.set(l[t],!1)}}mount(t){this.current=t,h.C.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),l.r.current||(0,a.U)(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||l.O.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,r.WG)(this.notifyUpdate),(0,r.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let o=r.fu.has(t);o&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&r.Gt.preRender(this.notifyUpdate),o&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in n.B){let e=n.B[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,s.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<c.length;e++){let i=c[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let o in e){let n=e[o],s=i[o];if((0,r.SS)(n))t.addValue(o,n);else if((0,r.SS)(s))t.addValue(o,(0,r.OQ)(n,{owner:t}));else if(s!==n)if(t.hasValue(o)){let e=t.getValue(o);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(o);t.addValue(o,(0,r.OQ)(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,r.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&((0,o.iW)(i)||(0,o.$X)(i))?i=parseFloat(i):!(0,r.tD)(i)&&r.f.test(e)&&(i=(0,r.Ju)(t,e)),this.setBaseTarget(t,(0,r.SS)(i)?i.get():i)),(0,r.SS)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=(0,d.a)(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let o=this.getBaseTargetFromProps(this.props,t);return void 0===o||(0,r.SS)(o)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:o}on(t,e){return this.events[t]||(this.events[t]=new o.vY),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}},44420:(t,e,i)=>{i.d(e,{$:()=>a});var r=i(78),o=i(38160),n=i(49267),s=i(44976);let a={pan:{Feature:o.f},drag:{Feature:r.w,ProjectionNode:s.P,MeasureLayout:n.$}}},44976:(t,e,i)=>{i.d(e,{P:()=>ty});var r=i(43891),o=i(21448),n=i(35580),s=i(46926),a=i(18070),l=i(73334),h=i(95902);let u=["TopLeft","TopRight","BottomLeft","BottomRight"],d=u.length,c=t=>"string"==typeof t?parseFloat(t):t,p=t=>"number"==typeof t||r.px.test(t);function m(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let f=g(0,.5,o.yT),v=g(.5,.95,o.lQ);function g(t,e,i){return r=>r<t?0:r>e?1:i((0,o.qB)(t,e,r))}function y(t,e){t.min=e.min,t.max=e.max}function x(t,e){y(t.x,e.x),y(t.y,e.y)}function T(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var O=i(96147),D=i(64200);function w(t,e,i,r,o){return t-=e,t=(0,O.hq)(t,1/i,r),void 0!==o&&(t=(0,O.hq)(t,1/o,r)),t}function S(t,e,[i,o,n],s,a){!function(t,e=0,i=1,o=.5,n,s=t,a=t){if(r.rq.test(e)&&(e=parseFloat(e),e=(0,r.k$)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let l=(0,r.k$)(s.min,s.max,o);t===s&&(l-=e),t.min=w(t.min,e,i,l,n),t.max=w(t.max,e,i,l,n)}(t,e[i],e[o],e[n],e.scale,s,a)}let P=["x","scaleX","originX"],E=["y","scaleY","originY"];function V(t,e,i,r){S(t.x,e,P,i?i.x:void 0,r?r.x:void 0),S(t.y,e,E,i?i.y:void 0,r?r.y:void 0)}var b=i(81786);function U(t){return 0===t.translate&&1===t.scale}function j(t){return U(t.x)&&U(t.y)}function C(t,e){return t.min===e.min&&t.max===e.max}function A(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function M(t,e){return A(t.x,e.x)&&A(t.y,e.y)}function L(t){return(0,D.CQ)(t.x)/(0,D.CQ)(t.y)}function N(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class F{constructor(){this.members=[]}add(t){(0,o.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,o.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var R=i(20637),B=i(94198),k=i(62662),_=i(29901);let $={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},I=["","X","Y","Z"],W={visibility:"hidden"},Q=0;function H(t,e,i,r){let{latestValues:o}=e;o[t]&&(i[t]=o[t],e.setStaticValue(t,0),r&&(r[t]=0))}function z({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:g,resetTransform:y}){return class{constructor(t={},i=e?.()){this.id=Q++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,r.Qu.value&&($.nodes=$.calculatedTargetDeltas=$.calculatedProjections=0),this.nodes.forEach(G),this.nodes.forEach(ti),this.nodes.forEach(tr),this.nodes.forEach(q),r.Qu.addProjectionMetrics&&r.Qu.addProjectionMetrics($)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new a.Y)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new o.vY),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,r.xZ)(e)&&!(0,r.h1)(e),this.instance=e;let{layoutId:i,layout:o,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(o||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=(0,l.c)(r,250),_.w.hasAnimatedSinceResize&&(_.w.hasAnimatedSinceResize=!1,this.nodes.forEach(te))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||o)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:o})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||th,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=n.getProps(),h=!this.targetLayout||!M(this.targetLayout,o),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,r.rU)(s,"layout"),onPlay:a,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||te(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=o})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,r.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(to),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let o=(0,s.P)(i);if(window.MotionHasOptimisedAnimation(o,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(o,"transform",r.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let o=this.getTransformTemplate();this.prevTransformTemplateValue=o?o(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Z);return}this.isUpdating||this.nodes.forEach(J),this.isUpdating=!1,this.nodes.forEach(tt),this.nodes.forEach(Y),this.nodes.forEach(X),this.clearAllSnapshots();let t=r.kB.now();r.uv.delta=(0,o.qE)(0,1e3/60,t-r.uv.timestamp),r.uv.timestamp=t,r.uv.isProcessing=!0,r.PP.update.process(r.uv),r.PP.preRender.process(r.uv),r.PP.render.process(r.uv),r.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r.k2.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(K),this.sharedNodes.forEach(tn)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,r.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){r.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||(0,D.CQ)(this.snapshot.measuredBox.x)||(0,D.CQ)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,b.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=g(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!y)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!j(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,k.HD)(this.latestValues)||o)&&(y(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),tc((e=r).x),tc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,b.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(tm))){let{scroll:t}=this.root;t&&((0,O.Ql)(e.x,t.offset.x),(0,O.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,b.ge)();if(x(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:o,options:n}=r;r!==this.root&&o&&n.layoutScroll&&(o.wasRoot&&x(e,t),(0,O.Ql)(e.x,o.offset.x),(0,O.Ql)(e.y,o.offset.y))}return e}applyTransform(t,e=!1){let i=(0,b.ge)();x(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&(0,O.Ww)(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),(0,k.HD)(r.latestValues)&&(0,O.Ww)(i,r.latestValues)}return(0,k.HD)(this.latestValues)&&(0,O.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,b.ge)();x(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,k.HD)(i.latestValues))continue;(0,k.vk)(i.latestValues)&&i.updateSnapshot();let r=(0,b.ge)();x(r,i.measurePageBox()),V(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return(0,k.HD)(this.latestValues)&&V(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==r.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:n}=this.options;if(this.layout&&(o||n)){if(this.resolvedRelativeTargetAt=r.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,b.ge)(),this.relativeTargetOrigin=(0,b.ge)(),(0,D.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),x(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,b.ge)(),this.targetWithTransforms=(0,b.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,D.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):x(this.target,this.layout.layoutBox),(0,O.o4)(this.target,this.targetDelta)):x(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,b.ge)(),this.relativeTargetOrigin=(0,b.ge)(),(0,D.jA)(this.relativeTargetOrigin,this.target,t.target),x(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}r.Qu.value&&$.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,k.vk)(this.parent.latestValues)||(0,k.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===r.uv.timestamp&&(i=!1),i)return;let{layout:o,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(o||n))return;x(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;(0,O.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,b.ge)());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(T(this.prevProjectionDelta.x,this.projectionDelta.x),T(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,D.vb)(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&N(this.projectionDelta.x,this.prevProjectionDelta.x)&&N(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),r.Qu.value&&$.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,b.xU)(),this.projectionDelta=(0,b.xU)(),this.projectionDeltaWithTransform=(0,b.xU)()}setAnimationOrigin(t,e=!1){let i,o=this.snapshot,n=o?o.latestValues:{},s={...this.latestValues},a=(0,b.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let l=(0,b.ge)(),h=(o?o.source:void 0)!==(this.layout?this.layout.source:void 0),g=this.getStack(),y=!g||g.members.length<=1,T=!!(h&&!y&&!0===this.options.crossfade&&!this.path.some(tl));this.animationProgress=0,this.mixTargetDelta=e=>{let o=e/1e3;if(ts(a.x,t.x,o),ts(a.y,t.y,o),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var g,O,w,S,P,E;(0,D.jA)(l,this.layout.layoutBox,this.relativeParent.layout.layoutBox),w=this.relativeTarget,S=this.relativeTargetOrigin,P=l,E=o,ta(w.x,S.x,P.x,E),ta(w.y,S.y,P.y,E),i&&(g=this.relativeTarget,O=i,C(g.x,O.x)&&C(g.y,O.y))&&(this.isProjectionDirty=!1),i||(i=(0,b.ge)()),x(i,this.relativeTarget)}h&&(this.animationValues=s,function(t,e,i,o,n,s){n?(t.opacity=(0,r.k$)(0,i.opacity??1,f(o)),t.opacityExit=(0,r.k$)(e.opacity??1,0,v(o))):s&&(t.opacity=(0,r.k$)(e.opacity??1,i.opacity??1,o));for(let n=0;n<d;n++){let s=`border${u[n]}Radius`,a=m(e,s),l=m(i,s);(void 0!==a||void 0!==l)&&(a||(a=0),l||(l=0),0===a||0===l||p(a)===p(l)?(t[s]=Math.max((0,r.k$)(c(a),c(l),o),0),(r.rq.test(l)||r.rq.test(a))&&(t[s]+="%")):t[s]=l)}(e.rotate||i.rotate)&&(t.rotate=(0,r.k$)(e.rotate||0,i.rotate||0,o))}(s,n,this.latestValues,o,T,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=o},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,r.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=r.Gt.update(()=>{_.w.hasAnimatedSinceResize=!0,r.qU.layout++,this.motionValue||(this.motionValue=(0,r.OQ)(0)),this.currentAnimation=(0,n.z)(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{r.qU.layout--},onComplete:()=>{r.qU.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:o}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&tp(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||(0,b.ge)();let e=(0,D.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=(0,D.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}x(e,i),(0,O.Ww)(e,o),(0,D.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,o)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new F),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&H("z",t,r,this.animationValues);for(let e=0;e<I.length;e++)H(`rotate${I[e]}`,t,r,this.animationValues),H(`skew${I[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return W;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=(0,h.u)(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=(0,h.u)(t?.pointerEvents)||""),this.hasProjected&&!(0,k.HD)(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",o=t.x.translate/e.x,n=t.y.translate/e.y,s=i?.z||0;if((o||n||s)&&(r=`translate3d(${o}px, ${n}px, ${s}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:o,rotateY:n,skewX:s,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),o&&(r+=`rotateX(${o}deg) `),n&&(r+=`rotateY(${n}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),i&&(e.transform=i(o,e.transform));let{x:n,y:s}=this.projectionDelta;for(let t in e.transformOrigin=`${100*n.origin}% ${100*s.origin}% 0`,r.animationValues?e.opacity=r===this?o.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:e.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,R.H){if(void 0===o[t])continue;let{correct:i,applyTo:n,isCSSVariable:s}=R.H[t],a="none"===e.transform?o[t]:i(o[t],r);if(n){let t=n.length;for(let i=0;i<t;i++)e[n[i]]=a}else s?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=r===this?(0,h.u)(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Z),this.root.sharedNodes.clear()}}}function Y(t){t.updateLayout()}function X(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:o}=t.options,n=e.source!==t.layout.source;"size"===o?(0,B.X)(t=>{let r=n?e.measuredBox[t]:e.layoutBox[t],o=(0,D.CQ)(r);r.min=i[t].min,r.max=r.min+o}):tp(o,e.layoutBox,i)&&(0,B.X)(r=>{let o=n?e.measuredBox[r]:e.layoutBox[r],s=(0,D.CQ)(i[r]);o.max=o.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+s)});let s=(0,b.xU)();(0,D.vb)(s,i,e.layoutBox);let a=(0,b.xU)();n?(0,D.vb)(a,t.applyTransform(r,!0),e.measuredBox):(0,D.vb)(a,i,e.layoutBox);let l=!j(s),h=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:o,layout:n}=r;if(o&&n){let s=(0,b.ge)();(0,D.jA)(s,e.layoutBox,o.layoutBox);let a=(0,b.ge)();(0,D.jA)(a,i,n.layoutBox),M(s,a)||(h=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function G(t){r.Qu.value&&$.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function q(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function K(t){t.clearSnapshot()}function Z(t){t.clearMeasurements()}function J(t){t.isLayoutDirty=!1}function tt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function te(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ti(t){t.resolveTargetDelta()}function tr(t){t.calcProjection()}function to(t){t.resetSkewAndRotation()}function tn(t){t.removeLeadSnapshot()}function ts(t,e,i){t.translate=(0,r.k$)(e.translate,0,i),t.scale=(0,r.k$)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function ta(t,e,i,o){t.min=(0,r.k$)(e.min,i.min,o),t.max=(0,r.k$)(e.max,i.max,o)}function tl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let th={duration:.45,ease:[.4,0,.1,1]},tu=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),td=tu("applewebkit/")&&!tu("chrome/")?Math.round:o.lQ;function tc(t){t.min=td(t.min),t.max=td(t.max)}function tp(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,D.HQ)(L(e),L(i),.2)}function tm(t){return t!==t.root&&t.scroll?.wasRoot}var tf=i(51442);let tv=z({attachResizeListener:(t,e)=>(0,tf.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tg={current:void 0},ty=z({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tg.current){let t=new tv({});t.mount(window),t.setOptions({layoutScroll:!0}),tg.current=t}return tg.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},45380:(t,e,i)=>{i(21448),i(43891),i(46756)},46756:(t,e,i)=>{i(43891),i(21448);let r={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};new WeakMap,new WeakMap,new WeakMap},49267:(t,e,i)=>{i.d(e,{$:()=>c});var r=i(43891),o=i(32082),n=i(90869),s=i(70797),a=i(29901);function l(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let h={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!r.px.test(t))return t;else t=parseFloat(t);let i=l(t,e.target.x),o=l(t,e.target.y);return`${i}% ${o}%`}};var u=i(20637);!function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();class d extends Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}()){componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:o}=t;(0,u.$)(p),o&&(e.group&&e.group.add(o),i&&i.register&&r&&i.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),a.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:o,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,o||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||r.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r.k2.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function c(t){let[e,i]=(0,o.xQ)(),r=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(n.L);return Object(function(){var t=Error("Cannot find module 'react/jsx-runtime'");throw t.code="MODULE_NOT_FOUND",t}())(d,{...t,layoutGroup:r,switchLayoutGroup:Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(s.N),isPresent:e,safeToRemove:i})}let p={borderRadius:{...h,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:h,borderTopRightRadius:h,borderBottomLeftRadius:h,borderBottomRightRadius:h,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let o=r.f.parse(t);if(o.length>5)return t;let n=r.f.createTransformer(t),s=+("number"!=typeof o[0]),a=i.x.scale*e.x,l=i.y.scale*e.y;o[0+s]/=a,o[1+s]/=l;let h=(0,r.k$)(a,l,.5);return"number"==typeof o[2+s]&&(o[2+s]/=h),"number"==typeof o[3+s]&&(o[3+s]/=h),n(o)}}}},51563:(t,e,i)=>{i.d(e,{W:()=>x});var r=i(6340),o=i(78660),n=i(5910),s=i(93045),a=i(65305),l=i(98312);let h=l._.length;var u=i(20419);let d=[...l.U].reverse(),c=l.U.length;function p(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function m(){return{animate:p(!0),whileInView:p(),whileHover:p(),whileTap:p(),whileDrag:p(),whileFocus:p(),exit:p()}}var f=i(52290);class v extends f.X{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(0,o._)(t,e,i))),i=m(),p=!0,f=e=>(i,r)=>{let o=(0,u.K)(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(o){let{transition:t,transitionEnd:e,...r}=o;i={...i,...r,...e}}return i};function v(o){let{props:m}=t,v=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<h;t++){let r=l._[t],o=e.props[r];((0,a.w)(o)||!1===o)&&(i[r]=o)}return i}(t.parent)||{},g=[],y=new Set,x={},T=1/0;for(let e=0;e<c;e++){var O,D;let l=d[e],h=i[l],u=void 0!==m[l]?m[l]:v[l],c=(0,a.w)(u),w=l===o?h.isActive:null;!1===w&&(T=e);let S=u===v[l]&&u!==m[l]&&c;if(S&&p&&t.manuallyAnimateOnMount&&(S=!1),h.protectedKeys={...x},!h.isActive&&null===w||!u&&!h.prevProp||(0,r.N)(u)||"boolean"==typeof u)continue;let P=(O=h.prevProp,"string"==typeof(D=u)?D!==O:!!Array.isArray(D)&&!(0,s.a)(D,O)),E=P||l===o&&h.isActive&&!S&&c||e>T&&c,V=!1,b=Array.isArray(u)?u:[u],U=b.reduce(f(l),{});!1===w&&(U={});let{prevResolvedValues:j={}}=h,C={...j,...U},A=e=>{E=!0,y.has(e)&&(V=!0,y.delete(e)),h.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=U[t],i=j[t];if(x.hasOwnProperty(t))continue;let r=!1;((0,n.p)(e)&&(0,n.p)(i)?(0,s.a)(e,i):e===i)?void 0!==e&&y.has(t)?A(t):h.protectedKeys[t]=!0:null!=e?A(t):y.add(t)}h.prevProp=u,h.prevResolvedValues=U,h.isActive&&(x={...x,...U}),p&&t.blockInitialAnimation&&(E=!1);let M=!(S&&P)||V;E&&M&&g.push(...b.map(t=>({animation:t,options:{type:l}})))}if(y.size){let e={};if("boolean"!=typeof m.initial){let i=(0,u.K)(t,Array.isArray(m.initial)?m.initial[0]:m.initial);i&&i.transition&&(e.transition=i.transition)}y.forEach(i=>{let r=t.getBaseTarget(i),o=t.getValue(i);o&&(o.liveStyle=!0),e[i]=r??null}),g.push({animation:e})}let w=!!g.length;return p&&(!1===m.initial||m.initial===m.animate)&&!t.manuallyAnimateOnMount&&(w=!1),p=!1,w?e(g):Promise.resolve()}return{animateChanges:v,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let o=v(e);for(let t in i)i[t].protectedKeys={};return o},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=m(),p=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,r.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let g=0;class y extends f.X{constructor(){super(...arguments),this.id=g++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let x={animation:{Feature:v},exit:{Feature:y}}},52290:(t,e,i)=>{i.d(e,{X:()=>r});class r{constructor(t){this.isMounted=!1,this.node=t}update(){}}},53292:(t,e,i)=>{i.d(e,{Z:()=>n});var r=i(44976),o=i(49267);let n={layout:{ProjectionNode:r.P,MeasureLayout:o.$}}},54160:(t,e,i)=>{i.d(e,{C:()=>x});var r=i(21014),o=i(43891),n=i(33055),s=i(29203);let a=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function l(t,e,i){for(let r in e)(0,o.SS)(e[r])||(0,n.z)(r,i)||(t[r]=e[r])}!function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var h=i(82076);let u=()=>({...a(),attrs:{}});var d=i(93095);!function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var c=i(99776),p=i(175);!function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var m=i(96488),f=i(60990);let v={useVisualState:(0,m.T)({scrapeMotionValuesFromProps:f.x,createRenderState:a})};var g=i(34527);let y={useVisualState:(0,m.T)({scrapeMotionValuesFromProps:g.x,createRenderState:u})};function x(t,e){return function(i,{forwardMotionProps:n}={forwardMotionProps:!1}){let m={...(0,p.Q)(i)?y:v,preloadedFeatures:t,useRender:function(t=!1){return(e,i,r,{latestValues:n},m)=>{let f=((0,p.Q)(e)?function(t,e,i,r){let o=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{let i=u();return(0,h.B)(i,e,(0,d.n)(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};l(e,t.style,t),o.style={...e,...o.style}}return o}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return l(r,i,t),Object.assign(r,function({transformTemplate:t},e){return Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>{let i=a();return(0,s.O)(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,m,e),v=(0,c.J)(i,"string"==typeof e,t),g=e!==Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())?{...v,...f,ref:r}:{},{children:y}=i,x=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(()=>(0,o.SS)(y)?y.get():y,[y]);return Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(e,{...g,children:x})}}(n),createVisualElement:e,Component:i};return(0,r.Z)(m)}}},54577:(t,e,i)=>{i.d(e,{d:()=>s});var r=i(43891);let o={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},n=r.Us.length;function s(t,e,i){let s="",a=!0;for(let l=0;l<n;l++){let n=r.Us[l],h=t[n];if(void 0===h)continue;let u=!0;if(!(u="number"==typeof h?h===+!!n.startsWith("scale"):0===parseFloat(h))||i){let t=(0,r.eK)(h,r.Wh[n]);if(!u){a=!1;let e=o[n]||n;s+=`${e}(${t}) `}i&&(e[n]=t)}}return s=s.trim(),i?s=i(e,a?"":s):a&&(s="none"),s}},55020:(t,e,i)=>{i.d(e,{PY1:()=>r.P}),i(60760),i(43050),i(51251),i(56787),i(14060),i(54160);var r=i(36545);i(26953),i(51586);var o=i(51563);i(96488),i(64200),i(81786),i(99776),i(68972),i(39174),i(97494),i(86811);var n=i(36324),s=i(59003);let a={renderer:s.J,...o.W,...n.n};var l=i(44420),h=i(53292);l.$,h.Z,s.J,o.W,i(95056),i(70749),i(90795),i(6464),i(8619),i(7302),i(97178),i(93486),i(62094),i(64132),i(78657),i(58492),i(95902),i(93084),i(3961),i(21448),i(198),i(71492),i(98828),i(98663),i(78660),i(32082),i(90693),i(93810),i(16242),i(21014),i(19394),i(74830),i(20637),i(87455),i(44976),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}(),i(54577),i(65511),i(40956),i(14087),i(30903),i(29240),i(74008),i(31788),i(1265),i(90869),i(51508),i(2999),i(80845),i(70797),i(18070),i(88558),i(2736),i(39126),i(80303),i(73334),i(19209),i(36464),i(55539),i(45380),i(46756),i(82182),i(49489),i(2986),i(43891)},59003:(t,e,i)=>{i.d(e,{J:()=>s}),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var r=i(75245),o=i(60728),n=i(175);let s=(t,e)=>(0,n.Q)(t)?new o.l(e):new r.M(e,{allowProjection:t!==Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())})},60728:(t,e,i)=>{i.d(e,{l:()=>c});var r=i(43891),o=i(81786),n=i(22397),s=i(78450),a=i(82076);let l=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var h=i(93095),u=i(20600),d=i(34527);class c extends n.b{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=o.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(r.fu.has(e)){let t=(0,r.Df)(e);return t&&t.default||0}return e=l.has(e)?e:(0,s.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,d.x)(t,e,i)}build(t,e,i){(0,a.B)(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in(0,u.e)(t,e,void 0,r),e.attrs)t.setAttribute(l.has(i)?i:(0,s.I)(i),e.attrs[i])}mount(t){this.isSVGTag=(0,h.n)(t.tagName),super.mount(t)}}},60990:(t,e,i)=>{i.d(e,{x:()=>n});var r=i(43891),o=i(33055);function n(t,e,i){let{style:n}=t,s={};for(let a in n)((0,r.SS)(n[a])||e.style&&(0,r.SS)(e.style[a])||(0,o.z)(a,t)||i?.getValue(a)?.liveStyle!==void 0)&&(s[a]=n[a]);return s}},62662:(t,e,i)=>{function r(t){return void 0===t||1===t}function o({scale:t,scaleX:e,scaleY:i}){return!r(t)||!r(e)||!r(i)}function n(t){return o(t)||s(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function s(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>n,vF:()=>s,vk:()=>o})},64200:(t,e,i)=>{i.d(e,{CQ:()=>o,HQ:()=>n,N:()=>h,jA:()=>d,vb:()=>a});var r=i(43891);function o(t){return t.max-t.min}function n(t,e,i){return Math.abs(t-e)<=i}function s(t,e,i,n=.5){t.origin=n,t.originPoint=(0,r.k$)(e.min,e.max,t.origin),t.scale=o(i)/o(e),t.translate=(0,r.k$)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,r){s(t.x,e.x,i.x,r?r.originX:void 0),s(t.y,e.y,i.y,r?r.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+o(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+o(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},65305:(t,e,i)=>{i.d(e,{w:()=>r});function r(t){return"string"==typeof t||Array.isArray(t)}},65511:(t,e,i)=>{i.d(e,{C:()=>r});let r=new WeakMap},74830:(t,e,i)=>{i.d(e,{S:()=>o});let r=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function o(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||r.has(t)}},75245:(t,e,i)=>{i.d(e,{M:()=>h});var r=i(43891),o=i(33757),n=i(22397),s=i(29203),a=i(20600),l=i(60990);class h extends n.b{constructor(){super(...arguments),this.type="html",this.renderInstance=a.e}readValueFromInstance(t,e){if(r.fu.has(e))return this.projection?.isProjecting?(0,r.zs)(e):(0,r.Ib)(t,e);{let i=window.getComputedStyle(t),o=((0,r.j4)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof o?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,o.m)(t,e)}build(t,e,i){(0,s.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,l.x)(t,e,i)}}},78450:(t,e,i)=>{i.d(e,{I:()=>r});let r=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},78588:(t,e,i)=>{function r({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function o({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function n(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}i.d(e,{FY:()=>r,bS:()=>n,pA:()=>o})},80131:(t,e,i)=>{},81786:(t,e,i)=>{i.d(e,{ge:()=>s,xU:()=>o});let r=()=>({translate:0,scale:1,origin:0,originPoint:0}),o=()=>({x:r(),y:r()}),n=()=>({min:0,max:0}),s=()=>({x:n(),y:n()})},82076:(t,e,i)=>{i.d(e,{B:()=>a});var r=i(29203),o=i(43891);let n={offset:"stroke-dashoffset",array:"stroke-dasharray"},s={offset:"strokeDashoffset",array:"strokeDasharray"};function a(t,{attrX:e,attrY:i,attrScale:a,pathLength:l,pathSpacing:h=1,pathOffset:u=0,...d},c,p,m){if((0,r.O)(t,d,p),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:f,style:v}=t;f.transform&&(v.transform=f.transform,delete f.transform),(v.transform||f.transformOrigin)&&(v.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),v.transform&&(v.transformBox=m?.transformBox??"fill-box",delete f.transformBox),void 0!==e&&(f.x=e),void 0!==i&&(f.y=i),void 0!==a&&(f.scale=a),void 0!==l&&function(t,e,i=1,r=0,a=!0){t.pathLength=1;let l=a?n:s;t[l.offset]=o.px.transform(-r);let h=o.px.transform(e),u=o.px.transform(i);t[l.array]=`${h} ${u}`}(f,l,h,u,!1)}},82182:(t,e,i)=>{i(43891)},87455:(t,e,i)=>{i(44976)},93095:(t,e,i)=>{i.d(e,{n:()=>r});let r=t=>"string"==typeof t&&"svg"===t.toLowerCase()},94198:(t,e,i)=>{i.d(e,{X:()=>r});function r(t){return[t("x"),t("y")]}},96147:(t,e,i)=>{i.d(e,{OU:()=>h,Ql:()=>u,Ww:()=>c,hq:()=>n,o4:()=>l});var r=i(43891),o=i(62662);function n(t,e,i){return i+e*(t-i)}function s(t,e,i,r,o){return void 0!==o&&(t=r+o*(t-r)),r+i*(t-r)+e}function a(t,e=0,i=1,r,o){t.min=s(t.min,e,i,r,o),t.max=s(t.max,e,i,r,o)}function l(t,{x:e,y:i}){a(t.x,e.translate,e.scale,e.originPoint),a(t.y,i.translate,i.scale,i.originPoint)}function h(t,e,i,r=!1){let n,s,a=i.length;if(a){e.x=e.y=1;for(let h=0;h<a;h++){s=(n=i[h]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&c(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,l(t,s)),r&&(0,o.HD)(n.latestValues)&&c(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function u(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,o,n=.5){let s=(0,r.k$)(t.min,t.max,n);a(t,e,i,s,o)}function c(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},96488:(t,e,i)=>{i.d(e,{T:()=>u}),function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}();var r=i(6340),o=i(2999),n=i(80845),s=i(19253),a=i(2735),l=i(82885),h=i(95902);let u=t=>(e,i)=>{let u=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(o.A),d=Object(function(){var t=Error("Cannot find module 'react'");throw t.code="MODULE_NOT_FOUND",t}())(n.t),c=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,o,n){return{latestValues:function(t,e,i,o){let n={},l=o(t,{});for(let t in l)n[t]=(0,h.u)(l[t]);let{initial:u,animate:d}=t,c=(0,s.e)(t),p=(0,s.O)(t);e&&p&&!c&&!1!==t.inherit&&(void 0===u&&(u=e.initial),void 0===d&&(d=e.animate));let m=!!i&&!1===i.initial,f=(m=m||!1===u)?d:u;if(f&&"boolean"!=typeof f&&!(0,r.N)(f)){let e=Array.isArray(f)?f:[f];for(let i=0;i<e.length;i++){let r=(0,a.a)(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=m?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,o,n,t),renderState:e()}})(t,e,u,d);return i?c():(0,l.M)(c)}},98312:(t,e,i)=>{i.d(e,{U:()=>r,_:()=>o});let r=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...r]},99776:(t,e,i)=>{i.d(e,{J:()=>n});var r=i(74830);let o=t=>!(0,r.S)(t);try{!function(t){"function"==typeof t&&(o=e=>e.startsWith("on")?!(0,r.S)(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}function n(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(o(s)||!0===i&&(0,r.S)(s)||!e&&!(0,r.S)(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}}}]);